rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {

    // Users collection - users can read/write their own user document
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }

    // Portfolios collection
    match /portfolios/{portfolioId} {
      // GET rule:
      // A user can GET a single document if it's public OR if they are the owner.
      // This rule protects private data.
      allow get: if resource.data.isPublished == true || (request.auth != null && request.auth.uid == resource.data.userId);

      // LIST rule:
      // Allow ANYONE (authenticated or not) to perform a query on the collection.
      // This is safe because our query itself (`where("isPublished", "==", true)`)
      // only asks for public documents, and the 'get' rule above provides the
      // final layer of protection on the data itself.
      allow list: if true;

      // CREATE rule:
      // A user can create a document if they are setting themselves as the owner.
      allow create: if request.auth != null && request.auth.uid == request.resource.data.userId;

      // UPDATE/DELETE rule:
      // A user can update or delete a document ONLY if they are the owner.
      allow update, delete: if request.auth != null && request.auth.uid == resource.data.userId;
    }
  }
}

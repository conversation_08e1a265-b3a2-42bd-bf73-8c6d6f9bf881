# Profolify Changelog

All notable changes to this project are documented here.

---

## [1.3.0] – 2025-08-01

### ✨ New Features
- **Real-time Portfolio Validation:** Comprehensive validation system that provides instant feedback as users edit their portfolios.
- **Qualification Fields:** Added education/certification/experience qualification fields to both Modern and Creative themes.
- **Smart Publish Button:** Publish button dynamically changes to "Fix Issues" when validation fails, with detailed error modal.

### 🛠️ Improvements
- **Instant Validation Feedback:** Validation errors now disappear immediately when fields are fixed, eliminating the need to click "Publish" twice.
- **Cross-Theme Consistency:** Both Modern and Creative themes now have identical validation integration across all components.
- **Enhanced About Sections:** Both themes now include qualification fields with conditional rendering (hidden when empty in published view).
- **Improved User Experience:** Real-time validation updates provide smooth, responsive editing experience.

### 🔧 Technical Enhancements
- **Automatic Validation Updates:** Validation re-runs automatically when form data changes, with performance optimization to prevent unnecessary re-renders.
- **Field-level Error Display:** All theme components integrate with validation system to show specific field errors.
- **Validation Architecture:** Comprehensive validation system with grouped errors, clear messaging, and direct field navigation.

### 📋 Validation Requirements
- **Required Fields:** Name, profession, email, bio, at least one qualification, minimum 3 skills, one experience, one project, profile image.
- **Format Validation:** Email format validation, optional phone number format checking.
- **Content Quality:** Ensures users create complete, professional portfolios before publishing.

---

## [1.2.0] – 2025-07-17

### ✨ New Features
- **Improved Publish Flow:** Publishing a portfolio now automatically opens the live site in a new tab and redirects the user to the dashboard for a smoother workflow.

### 🛠️ Improvements
- **Export Reliability:** Fixed a critical race condition in the live DOM capture system by increasing the render delay. This prevents incomplete or empty `index.html` files from being generated.
- **Theme CSS Scalability:** Refactored global CSS to use a generic `.theme-navbar` class for editor overrides, removing the need to add theme-specific rules and improving maintainability.

### 🐞 Bug Fixes
- **Theme Sync Errors:** Corrected the file path for the Modern theme's CSS in the sync configuration, resolving errors during the theme synchronization process.

---

## [1.1.0] – 2025-07-15

### ✨ New Features
- **Account Settings:** Comprehensive settings page for user profile and account management.
- **Account Deletion:** Users can now securely delete their account and all data.
- **Improved Google Auth:** Smoother sign-in flow and better loading states.

### 🛠️ Improvements
- **Security:** Enhanced user data protection and Firestore rules.
- **Performance:** Faster loading, cleaner URLs, and improved error handling.
- **Codebase:** Removed unused code, improved stability, and optimized state management.

### 🐞 Bug Fixes
- Fixed: Various issues reported by early users.
- Fixed: Edge cases in export and theme switching.
- Fixed: UI glitches on mobile devices.

---

## [1.0.0] – 2025-07-14

### 🚀 Initial Launch
- **Live Editing:** Real-time, WYSIWYG portfolio editor.
- **Theme System:** Modular, extensible themes (Modern, Creative Minimalist).
- **Export:** Pixel-perfect static export with Live DOM Capture.
- **Google Authentication:** Secure, one-click sign-in.
- **Cloudinary Integration:** Fast, optimized image delivery.
- **Mobile-First:** Fully responsive design.
- **Default Content:** Sample data for instant onboarding.

---

## [Unreleased]

- **Custom Domains:** (Planned) Map portfolios to user-owned domains.
- **Premium Themes:** (Planned) Paid themes and Stripe integration.
- **Theme Customization:** (Planned) User-driven color and font customization.
- **Email/Password Auth:** (Planned) Broader sign-in options.
- **Analytics:** (Planned) Usage and engagement tracking.

---

## How to Contribute

- Submit a pull request with your change.
- Add a new entry to this changelog under [Unreleased].

---

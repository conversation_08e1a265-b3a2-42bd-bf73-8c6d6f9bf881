# Profolify Export System – Live DOM Capture

This document explains how Profolify’s export system works, why it’s unique, and how to extend or debug it.

---

## 1. What Makes Profolify’s Export Unique?

- **Live DOM Capture:** Exports the *actual rendered* portfolio, not just a data-driven re-render.
- **Pixel-Perfect:** What you see in the editor is exactly what you get in the exported static site.
- **Zero Server Load:** All export logic runs in the browser.

---

## 2. Export Flow: Step-by-Step

1. **User Clicks Export**
   - Triggered from the dashboard or editor UI.

2. **Hidden Iframe Creation**
   - A hidden `<iframe>` is created in the browser.
   - The iframe loads the public portfolio URL (e.g., `/john-smith`).

3. **DOM Capture**
   - Once loaded, the script captures the `outerHTML` of the theme’s root element.
   - This ensures all styles, images, and layout are exactly as rendered.

4. **Sanitization & Optimization**
   - Removes all editor-only attributes (e.g., `contenteditable`).
   - Converts Next.js image URLs to standard `src` attributes (for Cloudinary).
   - Cleans up any dev-only or dynamic markup.

5. **Packaging**
   - Combines the clean HTML with the theme’s compiled CSS.
   - Uses [JSZip](https://stuk.github.io/jszip/) to package everything into a ZIP file.

6. **Download**
   - The user receives a ZIP containing:
     - `index.html` (the static site)
     - All CSS and assets needed for hosting anywhere

---

## 3. Technical Details

- **File:** `lib/live-dom-capture.ts`
- **Key Functions:**
  - `captureLiveDOM()`: Handles iframe creation and HTML capture.
  - `sanitizeHTML()`: Cleans and optimizes the captured HTML.
  - `packageExport()`: Bundles files with JSZip.

- **Dependencies:** Only runs in the browser; no server or Node.js code required.

---

## 4. How to Extend or Debug

- **Add New Theme Support:** Ensure your theme’s root element is uniquely identifiable for capture.
- **Add More Assets:** Update the packaging logic to include additional files (e.g., fonts, images).
- **Debugging:** Use browser dev tools to inspect the hidden iframe and captured HTML.

---

## 5. FAQ

**Q: Can I host the exported site anywhere?**  
A: Yes! The export is a fully static site—works on Netlify, Vercel, GitHub Pages, etc.

**Q: Will my export look exactly like my live portfolio?**  
A: Yes. The export system captures the *rendered* DOM, including all styles and images.

**Q: Can I customize the export?**  
A: Advanced users can edit the exported HTML/CSS before uploading to their host.

---

## 6. Summary

Profolify’s export system is fast, accurate, and requires no server resources.  
It’s designed for maximum fidelity and user control.

---
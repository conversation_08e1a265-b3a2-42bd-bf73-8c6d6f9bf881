# Theme Development Guide

Profolify’s theme system is modular, extensible, and designed for both rapid development and pixel-perfect export.

---

## 1. Theme System Overview

- **Themes** are self-contained packages of React components and CSS.
- **Dual-Mode Pattern:** Each theme component supports both editing (in the dashboard) and static (public/exported) modes.
- **CSS Architecture:** Modular in development, compiled for production/export.

---

## 2. Theme Directory Structure

themes/ my-awesome-theme/ components/ Hero.tsx About.tsx ... hero.css about.css my-awesome-theme-modular.css my-awesome-theme-compiled.css (auto-generated)


---

## 3. Step-by-Step: Creating a New Theme

### 1. Create the Theme Directory

- Inside `themes/`, create a new folder:  
  `themes/my-awesome-theme/`

### 2. Add Components

- In `components/`, create React components for each section (e.g., `Hero.tsx`, `About.tsx`).
- Each component should accept:
  - `isEditing: boolean` (toggles between editor and public mode)
  - `serverData?: PortfolioData` (for static rendering)
  - Use `EditableText` and other editor helpers for inline editing.

### 3. Add CSS

- Create a CSS file for each component (e.g., `hero.css`).
- In the root, create `my-awesome-theme-modular.css`:
  ``` css

  @import './components/hero.css';
  @import './components/about.css';
  /* ... */

  ```

### 4. Register the Theme

- Open themes/theme-registry.ts.
- Import your main theme component.
- Add a new entry to the THEME_REGISTRY array:

```json
{
  id: 'my-awesome-theme',
  name: 'My Awesome Theme',
  description: 'A bold, modern theme for creative professionals.',
  component: MyAwesomeTheme,
  preview: '/thumbnails/my-awesome-theme.jpg',
  modularCss: '/themes/my-awesome-theme/my-awesome-theme-modular.css',
  compiledCss: '/themes/my-awesome-theme/my-awesome-theme-compiled.css',
  isPremium: false // or true for paid themes
}
```

### 5. Compile and Preview

- Run:
``` bash npm run sync-themes ```

The dev server will hot-reload your theme.

### 4. Dual-Mode Component Pattern

Every theme component must support both editing and static modes:

```tsx
export default function Hero({ isEditing, serverData }) {
  const data = isEditing ? useEditorContext().state.formData : serverData;
  return (
    <section>
      <EditableText isEditing={isEditing} initialValue={data.title} />
      {isEditing && <ImageUploadButton />}
      {/* ... */}
    </section>
  );
}
```

- `isEditing = true`: Show inline editing, upload buttons, etc.
- `isEditing = false`: Render static HTML for public/exported site.

### 5. CSS Best Practices

- **Editor Navbar Override**: To ensure your theme's navbar renders correctly inside the portfolio editor canvas (preventing it from becoming `fixed` and obscuring the UI), add the generic `theme-navbar` class to your main navbar element alongside your theme-specific class. The editor uses this class to apply necessary style overrides.

  ```tsx
  // Example from ModernNavbar.tsx
  <header className="theme-modern-navbar theme-navbar">
    {/* ... */}
  </header>
  ```

- Use CSS variables for colors, fonts, and spacing to enable future customization.
- Keep component CSS modular and minimal.
- Use Tailwind CSS utility classes where possible for consistency.

### 6. Advanced: Dynamic Customization

- To support user-driven customization (colors, fonts), use CSS variables:
```css
:root {
  --theme-primary-color: #007bff;
  --theme-font-family: 'Inter', sans-serif;
}
```

```css
.hero-title {
  color: var(--theme-primary-color);
  font-family: var(--theme-font-family);
}
```

- Inject a `<style>` block in the page `<head>` to override variables based on user settings.


### 7. Export Compatibility

- Avoid using browser-only APIs or dynamic imports in theme components.
- All assets (images, fonts) should be web-accessible and referenced by URL.

### 8. How to Publish a Theme

- Add a preview image to /public/thumbnails/.
- Update the theme registry.
- Test in both editor and public/export modes.

### 9. Example: Minimal Theme Component

```tsx
import './hero.css';
import EditableText from '@/components/custom-ui/EditableText';

export default function Hero({ isEditing, serverData }) {
  const data = isEditing ? useEditorContext().state.formData : serverData;
  return (
    <section className="hero">
      <EditableText isEditing={isEditing} initialValue={data.title} />
      <p>{data.subtitle}</p>
    </section>
  );
}
```

### 10. Validation System Integration

**Real-time Portfolio Validation (v1.3.0+)**

All theme components must integrate with Profolify's validation system to provide real-time error feedback to users.

#### Required Integration Steps:

1. **Import Validation Context:**
```tsx
import { useEditorContext } from '@/contexts/EditorContext';
import { getFieldErrors } from '@/lib/portfolio-validation';
```

2. **Add Validation Props to EditableText:**
```tsx
const { state } = useEditorContext();
const getFieldError = (fieldName: string) => {
  if (!state.validationResult) return undefined;
  const errors = getFieldErrors(state.validationResult, fieldName);
  return errors.length > 0 ? errors[0].message : undefined;
};

// Example usage in component
<EditableText
  isEditing={isEditing}
  initialValue={data.bio}
  placeholder="Tell visitors about yourself..."
  hasError={!!getFieldError('bio')}
  errorMessage={getFieldError('bio')}
  onSave={(value) => handleUpdate('bio', value)}
/>
```

3. **Required Fields by Section:**
- **Hero**: `userName`, `profession`
- **About**: `bio`, at least one of `qualification1` or `qualification2`
- **Contact**: `email` (phone is optional)
- **Skills**: minimum 3 skills required
- **Experience**: minimum 1 experience entry required
- **Projects**: minimum 1 project required

#### Qualification Fields Implementation:

Both Modern and Creative themes must include qualification fields in their About components:

```tsx
// Helper function for qualification validation
const shouldShowQualificationError = (): boolean => {
  const qualificationError = getFieldError('qualifications');
  return !!(qualificationError && !data.qualification1 && !data.qualification2);
};

// Conditional rendering for qualifications
{(isEditing || data.qualification1 || data.qualification2) && (
  <div className="theme-qualifications">
    <h3>My Qualifications</h3>
    <div className="theme-qualifications-list">
      {(isEditing || data.qualification1) && (
        <div className="theme-qualification-item">
          <EditableText
            isEditing={isEditing}
            initialValue={data.qualification1 || ""}
            placeholder="e.g., Bachelor's in Computer Science"
            hasError={shouldShowQualificationError()}
            errorMessage={shouldShowQualificationError() ? getFieldError('qualifications') : undefined}
            onSave={(value) => handleUpdate('qualification1', value)}
          />
        </div>
      )}

      {(isEditing || data.qualification2) && (
        <div className="theme-qualification-item">
          <EditableText
            isEditing={isEditing}
            initialValue={data.qualification2 || ""}
            placeholder="e.g., 5+ years experience or certification"
            hasError={shouldShowQualificationError()}
            errorMessage={shouldShowQualificationError() && !data.qualification1 ? getFieldError('qualifications') : undefined}
            onSave={(value) => handleUpdate('qualification2', value)}
          />
        </div>
      )}
    </div>
  </div>
)}
```

#### Validation Best Practices:

- **Conditional Rendering**: Hide empty optional fields in published view
- **Error Display**: Show validation errors only when relevant
- **User Experience**: Provide clear, helpful error messages
- **Performance**: Use validation context efficiently to avoid unnecessary re-renders

### 11. Summary

- Themes are modular, dual-mode, and easy to extend.
- Use CSS variables for future customization.
- **Integrate validation system** for real-time user feedback.
- **Include qualification fields** in About components.
- Register and test your theme for both editing and export.

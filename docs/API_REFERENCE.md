# Profolify API Reference

This document details all API endpoints, data models, and integration points for Profolify.

---

## 1. API Structure

- **Next.js API Routes**: For server-side logic (theme compilation, migration, utilities).
- **Client SDKs**: Direct use of Firebase (Firestore, Auth) and Cloudinary.
- **Export System**: Client-side only, no server endpoint.

---

## 2. API Endpoints

### Theme Compilation

**POST `/api/sync-themes`**  
- **Purpose**: Compile and sync all theme CSS files.
- **Returns**: `{ success: boolean, message: string }`
- **Usage**: Build process and development.

### Migration

**POST `/api/migrate-slugs`**  
- **Purpose**: Migrate portfolio slugs to a clean format.
- **Returns**: Success/error response.
- **Usage**: One-time migration for existing portfolios.

---

## 3. Client-Side APIs

### Firebase User Management

- **getUser(uid: string): Promise<User | null>**
- **createOrUpdateUser(firebaseUser): Promise<User>**
- **updateUserProfile(uid, updates): Promise<void>**
- **deleteUserAccount(uid, firebaseUser): Promise<void>**

### Portfolio Management

- **getPortfolio(userId: string): Promise<PortfolioData | null>**
- **createPortfolioFromTemplate({ userId, userEmail, templateId }): Promise<PortfolioData>**
- **updatePortfolio({ userId, data }): Promise<void>**
- **deletePortfolio(userId: string): Promise<void>**
- **getPortfolioBySlug(slug: string): Promise<PortfolioData | null>**
- **uploadFile(file: File): Promise<string>** (Cloudinary)

### Theme Registry

- **getThemeComponent(templateId: string): React.ComponentType | null**
- **getAvailableThemes(): ThemeInfo[]**

---

## 4. Data Models

### User

```typescript
interface User {
  uid: string;
  email: string;
  displayName?: string;
  photoURL?: string;
  plan?: 'free' | 'pro';
  createdAt?: Date;
  updatedAt?: Date;
  // ...future: stripeCustomerId, customDomain, etc.
}
```

### PortfolioData

```typescript
interface PortfolioData {
  uid: string;
  isPublished: boolean;
  slug: string;
  templateId: string;
  userName: string;
  profession: string;
  about?: string;
  bio?: string;
  qualifications?: string;
  profileImageUrl?: string;
  resumeUrl?: string;
  projects: Project[];
  experiences: Experience[];
  skills: Skill[];
  socials: SocialLinks;
  contactEmail: string;
  // ...future: customization, analytics, etc.
}
```

### Project, Experience, Skill, SocialLinks

```typescript
interface Project {
  id: string;
  title: string;
  description: string;
  url?: string;
  liveUrl?: string;
  imageUrl?: string;
}

interface Experience {
  id: string;
  role: string;
  company: string;
  duration: string;
  description?: string;
  location?: string;
  companyUrl?: string;
}

interface Skill {
  id: string;
  name: string;
  category?: string;
}

type SocialLinks = {
  github?: string;
  linkedin?: string;
  twitter?: string;
  // etc.
};
```

### 5. Example Usage

Get User

```typescript
import { getUser } from '@/lib/user-api';
const user = await getUser('uid123');
```

Create Portfolio

```typescript
import { createPortfolioFromTemplate } from '@/lib/portfolio-api';
const portfolio = await createPortfolioFromTemplate({ userId, userEmail, templateId });
```

Upload File

```typescript
import { uploadFile } from '@/lib/portfolio-api';
const url = await uploadFile(file);
```

### 6. Error Handling

- All API calls throw on error; use try/catch for user feedback.
- Firestore rules enforce user isolation and data security.
- Auth errors are surfaced to the UI for re-authentication.

### 7. Integration Notes

- Authentication: All sensitive operations require a valid Firebase Auth session.
- Export: The export system is client-side only; no server endpoint is needed.
- Extending: Add new API routes in app/api/ for server-side logic, or use Firebase Functions (Blaze tier).

import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

export function middleware(request: NextRequest) {
  // Pass through API routes without authentication
  if (request.nextUrl.pathname.startsWith('/api')) {
    return NextResponse.next();
  }

  // Auto-sync theme CSS files in development mode
  if (process.env.NODE_ENV === 'development') {
    // Check if this is a request for theme CSS
    if (request.nextUrl.pathname.startsWith('/themes/') && request.nextUrl.pathname.endsWith('.css')) {
      console.log('🎨 Theme CSS requested:', request.nextUrl.pathname);
    }
  }

  const { pathname } = request.nextUrl;
  const token = request.cookies.get('firebaseIdToken')?.value;

  // --- FIX 1: Add the homepage '/' to the list of public routes ---
  const publicRoutes = ['/', '/login'];
  
  // Private routes
  const privateRoutes = ['/dashboard', '/portfolio'];

  // This logic is now more robust. It checks if the current path is one of the private/public routes.
  const isPrivateRoute = privateRoutes.some(route => pathname.startsWith(route));
  const isPublicRoute = publicRoutes.includes(pathname); // Using .includes() is better for exact matches like '/'

  // If user is not authenticated and tries to access a private route, redirect to login
  if (isPrivateRoute && !token) {
    const loginUrl = new URL('/login', request.url);
    loginUrl.searchParams.set('redirect', pathname);
    return NextResponse.redirect(loginUrl);
  }

  // If user IS authenticated and tries to access a public route (now including the homepage), redirect to dashboard
  if (isPublicRoute && token) {
    return NextResponse.redirect(new URL('/dashboard', request.url));
  }

  return NextResponse.next();
}

// Middleware matcher configuration
export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    '/((?!api|_next/static|_next/image|favicon.ico).*)',
  ],
};
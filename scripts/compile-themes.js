#!/usr/bin/env node

/**
 * Universal Theme Compiler
 * 
 * This script automatically detects and compiles all modular themes
 * without requiring theme-specific compilation scripts.
 * 
 * Features:
 * - Auto-detects themes with modular structure
 * - Compiles modular CSS into single files
 * - Supports any number of themes
 * - No manual configuration required
 */

const fs = require('fs');
const path = require('path');

/**
 * Standard component files that themes should have
 */
const STANDARD_COMPONENTS = [
  'navbar.css',
  'hero.css',
  'about.css',
  'experience.css',
  'skills.css',
  'projects.css',
  'contact.css',
  'footer.css'
];

/**
 * Detect all themes with modular structure
 */
function detectModularThemes() {
  const themesDir = path.join(process.cwd(), 'themes');
  const themes = [];
  
  if (!fs.existsSync(themesDir)) {
    console.warn('⚠️  Themes directory not found:', themesDir);
    return themes;
  }
  
  const entries = fs.readdirSync(themesDir, { withFileTypes: true });
  
  for (const entry of entries) {
    if (entry.isDirectory()) {
      const themeDir = path.join(themesDir, entry.name);
      const componentsDir = path.join(themeDir, 'components');
      const modularFile = path.join(themeDir, `${entry.name}-modular.css`);
      
      // Check if theme has modular structure
      if (fs.existsSync(componentsDir) && fs.existsSync(modularFile)) {
        themes.push({
          name: entry.name,
          dir: themeDir,
          componentsDir,
          modularFile,
          compiledFile: path.join(themeDir, `${entry.name}-compiled.css`)
        });
      }
    }
  }
  
  return themes;
}

/**
 * Get available component files for a theme
 */
function getThemeComponents(componentsDir) {
  const components = [];
  
  for (const componentFile of STANDARD_COMPONENTS) {
    const componentPath = path.join(componentsDir, componentFile);
    if (fs.existsSync(componentPath)) {
      components.push(componentFile);
    }
  }
  
  // Also check for any additional component files
  const allFiles = fs.readdirSync(componentsDir);
  for (const file of allFiles) {
    if (file.endsWith('.css') && !STANDARD_COMPONENTS.includes(file)) {
      components.push(file);
    }
  }
  
  return components;
}

/**
 * Compile a single theme
 */
function compileTheme(theme) {
  console.log(`🎨 Compiling ${theme.name} theme...`);
  
  // Read the modular CSS file
  if (!fs.existsSync(theme.modularFile)) {
    console.error(`❌ Modular CSS file not found: ${theme.modularFile}`);
    return false;
  }
  
  const modularContent = fs.readFileSync(theme.modularFile, 'utf8');
  const components = getThemeComponents(theme.componentsDir);
  
  let compiledContent = `/* ${theme.name.charAt(0).toUpperCase() + theme.name.slice(1)} Theme - Compiled CSS */\n\n`;
  
  // Add each component's CSS
  for (const componentFile of components) {
    const componentPath = path.join(theme.componentsDir, componentFile);
    
    if (fs.existsSync(componentPath)) {
      console.log(`📄 Adding ${componentFile}...`);
      const componentContent = fs.readFileSync(componentPath, 'utf8');
      
      const sectionName = componentFile.toUpperCase().replace('.CSS', '');
      compiledContent += `/* ===== ${sectionName} STYLES ===== */\n`;
      compiledContent += componentContent;
      compiledContent += '\n\n';
    }
  }
  
  // Add the base styles from modular file (everything after the imports)
  const lines = modularContent.split('\n');
  let addingBaseStyles = false;
  let baseStyles = '';
  
  for (const line of lines) {
    if (line.trim().startsWith('@import')) {
      continue; // Skip import statements
    }
    
    // Look for base styles section
    if (line.trim().includes('Base styles') || 
        line.trim().includes(`theme-${theme.name}-root`) ||
        line.trim().includes('Container utilities') ||
        line.trim().includes('Utility classes')) {
      addingBaseStyles = true;
    }
    
    if (addingBaseStyles) {
      baseStyles += line + '\n';
    }
  }
  
  if (baseStyles.trim()) {
    compiledContent += '/* ===== BASE STYLES ===== */\n';
    compiledContent += baseStyles;
  }
  
  // Write the compiled CSS
  fs.writeFileSync(theme.compiledFile, compiledContent);
  
  const stats = fs.statSync(theme.compiledFile);
  console.log(`✅ Compiled: ${theme.compiledFile} (${stats.size} bytes)`);
  
  return true;
}

/**
 * Compile all detected themes
 */
function compileAllThemes() {
  console.log('🚀 Starting universal theme compilation...\n');
  
  const themes = detectModularThemes();
  
  if (themes.length === 0) {
    console.log('ℹ️  No modular themes detected.');
    return true;
  }
  
  console.log(`📋 Found ${themes.length} modular theme(s):`);
  themes.forEach(theme => console.log(`   - ${theme.name}`));
  console.log('');
  
  let allSuccessful = true;
  
  for (const theme of themes) {
    try {
      const success = compileTheme(theme);
      if (!success) {
        allSuccessful = false;
      }
    } catch (error) {
      console.error(`❌ Failed to compile ${theme.name}:`, error.message);
      allSuccessful = false;
    }
    console.log(''); // Add spacing between themes
  }
  
  if (allSuccessful) {
    console.log('🎉 All themes compiled successfully!');
  } else {
    console.log('⚠️  Some themes failed to compile.');
  }
  
  return allSuccessful;
}

// Run the compilation if called directly
if (require.main === module) {
  const success = compileAllThemes();
  if (!success) {
    process.exit(1);
  }
}

module.exports = { 
  compileAllThemes, 
  detectModularThemes, 
  compileTheme 
};

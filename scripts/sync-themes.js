#!/usr/bin/env node

/**
 * Theme Sync Script
 *
 * This script compiles modular CSS and syncs CSS files from themes directory
 * to public directory and validates all theme configurations.
 *
 * Usage:
 *   node scripts/sync-themes.js
 *   npm run sync-themes
 */

const fs = require('fs');
const path = require('path');
const { compileAllThemes, detectModularThemes } = require('./compile-themes.js');

/**
 * Auto-detect themes and generate theme registry data
 */
function getThemeRegistryData() {
  const themes = detectModularThemes();
  const registryData = [];

  for (const theme of themes) {
    registryData.push({
      id: `${theme.name}-theme-v1`,
      name: theme.name.charAt(0).toUpperCase() + theme.name.slice(1).replace('-', ' '),
      sourceCssFile: theme.compiledFile.replace(process.cwd() + '/', ''),
      cssFile: `/${theme.compiledFile.replace(process.cwd() + '/', '')}`,
    });
  }

  return registryData;
}

/**
 * Sync CSS files from themes directory to public directory
 */
function syncThemeCssFiles() {
  console.log('🎨 Compiling and syncing theme CSS files...');

  // Compile all themes using universal compiler
  console.log('📦 Compiling all themes...');
  try {
    compileAllThemes();
  } catch (error) {
    console.warn('⚠️  Failed to compile themes:', error.message);
  }

  // Get auto-detected themes
  const THEMES = getThemeRegistryData();

  for (const theme of THEMES) {
    try {
      const sourcePath = path.join(process.cwd(), theme.sourceCssFile);
      const publicPath = path.join(process.cwd(), 'public', theme.cssFile);
      
      // Ensure public directory exists
      const publicDir = path.dirname(publicPath);
      if (!fs.existsSync(publicDir)) {
        fs.mkdirSync(publicDir, { recursive: true });
      }
      
      // Copy source CSS to public directory
      if (fs.existsSync(sourcePath)) {
        fs.copyFileSync(sourcePath, publicPath);
        
        // Get file sizes for comparison
        const sourceStats = fs.statSync(sourcePath);
        const publicStats = fs.statSync(publicPath);
        
        console.log(`✅ Synced ${theme.name}:`);
        console.log(`   Source: ${theme.sourceCssFile} (${sourceStats.size} bytes)`);
        console.log(`   Public: public${theme.cssFile} (${publicStats.size} bytes)`);
      } else {
        console.warn(`⚠️  Source CSS file not found for ${theme.name}: ${sourcePath}`);
      }
    } catch (error) {
      console.error(`❌ Failed to sync CSS for ${theme.name}:`, error.message);
    }
  }
  
  console.log('✨ Theme CSS sync completed!');
}

/**
 * Validate theme files exist
 */
function validateThemeFiles() {
  console.log('\n🔍 Validating theme files...');

  let allValid = true;
  const THEMES = getThemeRegistryData();

  for (const theme of THEMES) {
    const sourcePath = path.join(process.cwd(), theme.sourceCssFile);
    const publicPath = path.join(process.cwd(), 'public', theme.cssFile);
    
    console.log(`\n📋 Checking ${theme.name}:`);
    
    // Check source file
    if (fs.existsSync(sourcePath)) {
      const stats = fs.statSync(sourcePath);
      console.log(`   ✅ Source CSS: ${theme.sourceCssFile} (${stats.size} bytes)`);
    } else {
      console.log(`   ❌ Source CSS: ${theme.sourceCssFile} (NOT FOUND)`);
      allValid = false;
    }
    
    // Check public file
    if (fs.existsSync(publicPath)) {
      const stats = fs.statSync(publicPath);
      console.log(`   ✅ Public CSS: public${theme.cssFile} (${stats.size} bytes)`);
    } else {
      console.log(`   ❌ Public CSS: public${theme.cssFile} (NOT FOUND)`);
      allValid = false;
    }
    
    // Check if files are in sync
    if (fs.existsSync(sourcePath) && fs.existsSync(publicPath)) {
      const sourceStats = fs.statSync(sourcePath);
      const publicStats = fs.statSync(publicPath);
      
      if (sourceStats.size === publicStats.size && sourceStats.mtime <= publicStats.mtime) {
        console.log(`   ✅ Files are in sync`);
      } else {
        console.log(`   ⚠️  Files may be out of sync (different sizes or timestamps)`);
      }
    }
  }
  
  if (allValid) {
    console.log('\n✅ All theme files are valid!');
  } else {
    console.log('\n❌ Some theme files are missing or invalid!');
    process.exit(1);
  }
}

/**
 * Touches a file to trigger Next.js hot reload.
 */
function triggerHotReload() {
  const fileToTouch = path.join(process.cwd(), 'app', 'globals.css');
  try {
    const time = new Date();
    fs.utimesSync(fileToTouch, time, time);
    console.log(`\n🔄 Touched ${path.basename(fileToTouch)} to trigger hot reload.`);
  } catch (error) {
    if (error.code === 'ENOENT') {
      fs.closeSync(fs.openSync(fileToTouch, 'w'));
      console.log(`\n✨ Created and touched ${path.basename(fileToTouch)} to trigger hot reload.`);
    } else {
      console.warn(`\n⚠️  Could not touch ${path.basename(fileToTouch)} for hot reload:`, error.message);
    }
  }
}

/**
 * Main function
 */
function main() {
  const args = process.argv.slice(2);

  if (args.includes('--validate-only')) {
    validateThemeFiles();
  } else {
    syncThemeCssFiles();
    validateThemeFiles();
    triggerHotReload();
  }
}

// Run the script
main();

/* Modern Theme - About Component */

.theme-modern-about {
  padding: 6rem 1rem;
  position: relative;
}

.theme-modern-about-container {
  max-width: 1200px;
  margin: 0 auto;
  display: grid;
  gap: 4rem;
  align-items: center;
  position: relative;
  z-index: 1;
}

@media (min-width: 1024px) {
  .theme-modern-about-container {
    grid-template-columns: 1.2fr 1fr;
    gap: 6rem;
  }
}

.theme-modern-about-content {
  text-align: center;
}

@media (min-width: 1024px) {
  .theme-modern-about-content {
    text-align: left;
  }
}

.theme-modern-about-header {
  margin-bottom: 2rem;
}

.theme-modern-about-title {
  font-size: 2.5rem;
  font-weight: 900;
  color: #ffffff;
  margin-bottom: 1rem;
  line-height: 1.1;
  letter-spacing: -0.025em;
  background: linear-gradient(135deg, #ffffff 0%, #e2e8f0 25%, #60a5fa 50%, #a78bfa 75%, #f472b6 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

/* Solid color for editing mode */
.theme-modern-about-title[contenteditable="true"] {
  color: #ffffff;
  background: none;
  -webkit-text-fill-color: #ffffff;
}

@media (min-width: 640px) {
  .theme-modern-about-title {
    font-size: 3rem;
    letter-spacing: -0.05em;
  }
}

@media (min-width: 1024px) {
  .theme-modern-about-title {
    font-size: 3.5rem;
    letter-spacing: -0.075em;
  }
}

.theme-modern-about-divider {
  width: 4rem;
  height: 0.25rem;
  background: linear-gradient(90deg, #60a5fa 0%, #a78bfa 100%);
  border-radius: 0.125rem;
  margin: 0 auto;
}

@media (min-width: 1024px) {
  .theme-modern-about-divider {
    margin: 0;
  }
}

.theme-modern-about-body {
  max-width: 600px;
  margin: 0 auto;
}

@media (min-width: 1024px) {
  .theme-modern-about-body {
    margin: 0;
  }
}

.theme-modern-about-text {
  font-size: 1.125rem;
  color: #cbd5e1;
  line-height: 1.8;
  margin: 0;
  font-weight: 400;
  opacity: 0.9;
}

@media (min-width: 640px) {
  .theme-modern-about-text {
    font-size: 1.25rem;
  }
}

.theme-modern-about-image-container {
  display: flex;
  justify-content: center;
  align-items: center;
}

.theme-modern-about-image-wrapper {
  position: relative;
  width: 100%;
  max-width: 400px;
  aspect-ratio: 1;
  border-radius: 1.5rem;
  overflow: hidden;
  background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
  padding: 0.5rem;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.5);
}

.theme-modern-about-image-wrapper::before {
  content: '';
  position: absolute;
  inset: -0.25rem;
  background: linear-gradient(135deg, #60a5fa 0%, #a78bfa 50%, #f472b6 100%);
  border-radius: 1.75rem;
  opacity: 0.3;
  z-index: -1;
  transition: all 0.3s ease;
}

.theme-modern-about-image-wrapper:hover::before {
  opacity: 0.5;
  transform: scale(1.02);
}

.theme-modern-about-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 1rem;
  transition: all 0.3s ease;
  border: 2px solid rgba(96, 165, 250, 0.2);
}

.theme-modern-about-image-wrapper:hover .theme-modern-about-image {
  transform: scale(1.02);
  border-color: rgba(96, 165, 250, 0.4);
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .theme-modern-about {
    padding: 4rem 1rem;
  }

  .theme-modern-about-container {
    gap: 3rem;
  }

  .theme-modern-about-title {
    font-size: 2rem;
  }

  .theme-modern-about-text {
    font-size: 1rem;
  }

  .theme-modern-about-image-wrapper {
    max-width: 300px;
  }
}

/* Qualifications Section */
.theme-modern-about-qualifications {
  margin-top: 2.5rem;
  padding-top: 2rem;
  border-top: 1px solid rgba(96, 165, 250, 0.2);
}

.theme-modern-about-qualifications-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: #ffffff;
  margin-bottom: 1.5rem;
  background: linear-gradient(135deg, #60a5fa 0%, #a78bfa 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

/* Solid color for editing mode */
.theme-modern-about-qualifications-title[contenteditable="true"] {
  color: #ffffff;
  background: none;
  -webkit-text-fill-color: #ffffff;
}

.theme-modern-about-qualifications-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.theme-modern-about-qualification-item {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  padding: 0.75rem 1rem;
  background: rgba(30, 41, 59, 0.5);
  border: 1px solid rgba(96, 165, 250, 0.2);
  border-radius: 0.75rem;
  transition: all 0.3s ease;
}

.theme-modern-about-qualification-item:hover {
  background: rgba(30, 41, 59, 0.7);
  border-color: rgba(96, 165, 250, 0.3);
  transform: translateY(-1px);
}

.theme-modern-about-qualification-bullet {
  color: #60a5fa;
  font-weight: 600;
  font-size: 1.25rem;
  line-height: 1;
  margin-top: 0.125rem;
  flex-shrink: 0;
}

.theme-modern-about-qualification-text {
  color: #cbd5e1;
  font-size: 1rem;
  line-height: 1.6;
  font-weight: 400;
  flex: 1;
  margin: 0;
}

@media (min-width: 640px) {
  .theme-modern-about-qualification-text {
    font-size: 1.125rem;
  }
}

/* Responsive adjustments for qualifications */
@media (max-width: 640px) {
  .theme-modern-about-qualifications {
    margin-top: 2rem;
    padding-top: 1.5rem;
  }

  .theme-modern-about-qualifications-title {
    font-size: 1.25rem;
    margin-bottom: 1rem;
  }

  .theme-modern-about-qualification-item {
    padding: 0.5rem 0.75rem;
    gap: 0.5rem;
  }

  .theme-modern-about-qualification-text {
    font-size: 0.875rem;
  }
}

/* Modern Theme - Footer Component */

.theme-modern-footer {
  background: #1f2937;
  color: #d1d5db;
  padding: 3rem 1rem 2rem;
  text-align: center;
}

.theme-modern-footer-container {
  max-width: 1200px;
  margin: 0 auto;
}

.theme-modern-footer-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.theme-modern-footer-text {
  margin-bottom: 0;
}


/* Align “Powered by” + link on the same line */
.theme-modern-footer-attribution {
  display: flex;
  align-items: center;
  gap: 0.4rem;
  font-size: 0.875rem;
}

.theme-modern-footer-attribution a {
  text-decoration: none;
  background: linear-gradient(135deg, #60a5fa 0%, #a78bfa 50%, #f472b6 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  transition: all 0.3s ease;
  letter-spacing: -0.025em;
  display: inline-flex;
  align-items: center;
  gap: 0.4rem;
}

/* Hide 'Powered by' section in exported version */
[data-export] .theme-modern-footer-attribution {
  display: none !important;
}

.theme-modern-footer-link {
  color: #a78bfa;
  text-decoration: none;
  transition: color 0.3s ease;
}

.theme-modern-footer-logo {
  display: inline-block;
  vertical-align: middle;
  margin-right: 0.25rem;
  margin-left: 0.25rem;
}

.theme-modern-footer-link:hover {
  color: #93c5fd;
}

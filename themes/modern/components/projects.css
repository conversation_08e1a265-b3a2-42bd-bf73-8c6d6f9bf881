/* Modern Theme - Projects Section - Completely Refactored */

/* Shared Editable Field Styles */
/* Base editable field styling - only applies when contenteditable */
.editable-field[contenteditable="true"] {
  position: relative;
  border: 2px dashed rgba(102, 126, 234, 0.3);
  border-radius: 0.375rem;
  padding: 0.5rem;
  background: rgba(102, 126, 234, 0.05);
  transition: all 0.3s ease;
  min-height: 1.5rem;
  cursor: text;
}

.editable-field[contenteditable="true"]:hover {
  border-color: rgba(102, 126, 234, 0.5);
  background: rgba(102, 126, 234, 0.1);
}

.editable-field[contenteditable="true"]:focus {
  outline: none;
  border-color: #667eea;
  background: rgba(102, 126, 234, 0.1);
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* Placeholder styling for empty editable fields */
.editable-field[contenteditable="true"]:empty:before {
  content: attr(data-placeholder);
  color: #9ca3af;
  font-style: italic;
  pointer-events: none;
}

/* Inline editable fields (for smaller text elements) - only applies when contenteditable */
.editable-field-inline[contenteditable="true"] {
  position: relative;
  border: 1px dashed rgba(102, 126, 234, 0.3);
  border-radius: 0.25rem;
  padding: 0.25rem 0.5rem;
  background: rgba(102, 126, 234, 0.05);
  transition: all 0.3s ease;
  min-height: 1rem;
  cursor: text;
  display: inline-block;
}

.editable-field-inline[contenteditable="true"]:hover {
  border-color: rgba(102, 126, 234, 0.5);
  background: rgba(102, 126, 234, 0.1);
}

.editable-field-inline[contenteditable="true"]:focus {
  outline: none;
  border-color: #667eea;
  background: rgba(102, 126, 234, 0.1);
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
}

.editable-field-inline[contenteditable="true"]:empty:before {
  content: attr(data-placeholder);
  color: #9ca3af;
  font-style: italic;
  pointer-events: none;
}

/* Large editable fields (for descriptions, content areas) - only applies when contenteditable */
.editable-field-large[contenteditable="true"] {
  position: relative;
  border: 2px dashed rgba(102, 126, 234, 0.3);
  border-radius: 0.5rem;
  padding: 1rem;
  background: rgba(102, 126, 234, 0.05);
  transition: all 0.3s ease;
  min-height: 3rem;
  cursor: text;
}

.editable-field-large[contenteditable="true"]:hover {
  border-color: rgba(102, 126, 234, 0.5);
  background: rgba(102, 126, 234, 0.1);
}

.editable-field-large[contenteditable="true"]:focus {
  outline: none;
  border-color: #667eea;
  background: rgba(102, 126, 234, 0.1);
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.editable-field-large[contenteditable="true"]:empty:before {
  content: attr(data-placeholder);
  color: #9ca3af;
  font-style: italic;
  pointer-events: none;
}

/* Modern theme variant (blue gradient) - only applies when contenteditable */
.editable-field-modern[contenteditable="true"] {
  border-color: rgba(59, 130, 246, 0.3);
  background: rgba(59, 130, 246, 0.05);
}

.editable-field-modern[contenteditable="true"]:hover {
  border-color: rgba(59, 130, 246, 0.5);
  background: rgba(59, 130, 246, 0.1);
}

.editable-field-modern[contenteditable="true"]:focus {
  border-color: #3b82f6;
  background: rgba(59, 130, 246, 0.1);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Ensure long project titles wrap correctly */
.modern-project-title {
  overflow-wrap: break-word;
  word-break: break-word; /* For better browser compatibility */
  min-width: 0; /* Allow the element to shrink and wrap */
}

/* Main Section */
.modern-projects {
  padding: 5rem 0;
  position: relative;
}

.modern-projects-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
  position: relative;
  z-index: 1;
}

/* Section Header */
.modern-projects-header {
  text-align: center;
  margin-bottom: 4rem;
}

.modern-projects-title {
  font-size: 3rem;
  font-weight: 900;
  color: #ffffff;
  margin-bottom: 1rem;
  line-height: 1.1;
  letter-spacing: -0.025em;
  background: linear-gradient(135deg, #ffffff 0%, #e2e8f0 25%, #60a5fa 50%, #a78bfa 75%, #f472b6 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

@media (min-width: 640px) {
  .modern-projects-title {
    font-size: 3.5rem;
    letter-spacing: -0.05em;
  }
}

@media (min-width: 1024px) {
  .modern-projects-title {
    font-size: 4rem;
    letter-spacing: -0.075em;
  }
}

.modern-projects-divider {
  width: 4rem;
  height: 0.25rem;
  background: linear-gradient(90deg, #60a5fa 0%, #a78bfa 100%);
  border-radius: 0.125rem;
  margin: 0 auto 1.5rem;
}

.modern-projects-subtitle {
  font-size: 1.25rem;
  color: #cbd5e1;
  line-height: 1.6;
  max-width: 600px;
  margin: 0 auto;
  opacity: 0.9;
}

@media (min-width: 768px) {
  .modern-projects-subtitle {
    font-size: 1.375rem;
  }
}

/* Projects Grid */
.modern-projects-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 2rem;
  max-width: 1000px;
  margin: 0 auto;
}

@media (min-width: 768px) {
  .modern-projects-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 2.5rem;
  }
}

@media (min-width: 1024px) {
  .modern-projects-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

/* Project Cards */
.modern-project-card {
  background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
  border-radius: 1rem;
  overflow: hidden;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(96, 165, 250, 0.2);
  transition: all 0.3s ease;
  position: relative;
}

.modern-project-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(96, 165, 250, 0.05) 0%, rgba(168, 139, 250, 0.05) 100%);
  pointer-events: none;
}

.modern-project-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 25px 60px rgba(0, 0, 0, 0.4);
  border-color: rgba(96, 165, 250, 0.4);
}

/* Project Image */
.modern-project-image-container {
  position: relative;
  width: 100%;
  height: 250px;
  overflow: hidden;
  background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
}

.modern-project-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: all 0.3s ease;
}

.modern-project-image-uploading {
  opacity: 0.5;
}

/* Upload Overlay */
.modern-project-upload-overlay {
  position: absolute;
  inset: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
  cursor: pointer;
  z-index: 10;
}

.modern-project-card:hover .modern-project-upload-overlay {
  opacity: 1;
}

.modern-project-upload-input {
  position: absolute;
  inset: 0;
  opacity: 0;
  cursor: pointer;
}

.modern-project-upload-content,
.modern-project-upload-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  color: #ffffff;
}

.modern-project-upload-icon {
  width: 2rem;
  height: 2rem;
}

.modern-project-upload-text {
  font-size: 0.875rem;
  font-weight: 500;
}

/* Loading States */
.modern-project-loading-overlay {
  position: absolute;
  inset: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 20;
}

.modern-project-loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  color: #ffffff;
}

.modern-project-loading-spinner {
  width: 2rem;
  height: 2rem;
  animation: spin 1s linear infinite;
}

.modern-project-loading-text {
  font-size: 0.875rem;
  font-weight: 500;
}

/* Spinner Animation */
@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.modern-spinner {
  animation: spin 1s linear infinite;
}

/* Project Content */
.modern-project-content {
  padding: 1.5rem;
  position: relative;
  z-index: 1;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

/* Project Header */
.modern-project-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
  gap: 0.5rem;
}

.modern-project-title-section {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  flex: 1;
  min-width: 0;
}

.modern-project-folder-icon {
  color: #60a5fa;
  flex-shrink: 0;
}

/* Project Title */
.modern-project-title {
  font-size: 1.25rem;
  font-weight: 700;
  color: #ffffff;
  margin: 0;
  line-height: 1.4;
  flex: 1;
}

/* Delete Button */
.modern-project-delete-btn {
  background: rgb(237, 96, 96);
  border: 1px solid rgba(239, 68, 68, 0.3);
  color: #ffffff;
  padding: 0.5rem;
  border-radius: 0.375rem;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  width: 2.5rem;
  height: 2.5rem;
}

.modern-project-delete-btn:hover {
  background: rgba(239, 68, 68, 0.2);
  border-color: rgba(239, 68, 68, 0.5);
  transform: scale(1.05);
}

/* Project Description */
.modern-project-description {
  flex: 1;
}

.modern-project-description-text {
  font-size: 0.95rem;
  color: #cbd5e1;
  line-height: 1.6;
  margin: 0;
  white-space: pre-line;
  min-height: 4rem;
  overflow-wrap: break-word;
  word-break: break-word;
}

/* Project URL */
.modern-project-url {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-top: auto;
}

.modern-project-url-input {
  flex: 1;
  min-width: 0;
  font-size: 0.875rem;
  color: #94a3b8;
}

.modern-project-link {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  color: #60a5fa;
  text-decoration: none;
  font-weight: 500;
  font-size: 0.875rem;
  padding: 0.5rem 0.75rem;
  background: rgba(96, 165, 250, 0.1);
  border: 1px solid rgba(96, 165, 250, 0.3);
  border-radius: 0.375rem;
  transition: all 0.3s ease;
  flex-shrink: 0;
  white-space: nowrap;
}

.modern-project-link:hover {
  background: rgba(96, 165, 250, 0.2);
  border-color: rgba(96, 165, 250, 0.5);
  transform: translateY(-1px);
}

/* Add Project Section */
.modern-projects-add-section {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  border: 2px dashed rgba(96, 165, 250, 0.3);
  border-radius: 1rem;
  background: rgba(96, 165, 250, 0.05);
  transition: all 0.3s ease;
}

.modern-projects-add-section:hover {
  border-color: rgba(96, 165, 250, 0.5);
  background: rgba(96, 165, 250, 0.1);
}

.modern-projects-add-btn {
  background: linear-gradient(135deg, #60a5fa 0%, #a78bfa 100%);
  border: none;
  color: #ffffff;
  padding: 1.5rem 2rem;
  border-radius: 0.75rem;
  font-size: 1.125rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  box-shadow: 0 4px 20px rgba(96, 165, 250, 0.3);
}

.modern-projects-add-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 30px rgba(96, 165, 250, 0.4);
}

/* Enhanced Editing Experience */
.modern-project-card {
  min-height: 480px;
  display: flex;
  flex-direction: column;
}

.modern-project-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 1.25rem;
}

/* Better spacing for editable fields */
.modern-project-title.editable-field {
  min-height: 2.5rem;
  display: flex;
  align-items: center;
  font-size: 1.25rem;
}

.modern-project-description-text.editable-field-large {
  min-height: 6rem;
  line-height: 1.6;
  font-size: 0.95rem;
}

.modern-project-url-input.editable-field-inline {
  min-height: 2.5rem;
  display: flex;
  align-items: center;
  font-size: 0.875rem;
}

/* Improved visual hierarchy in edit mode */
.modern-project-header {
  border-bottom: 1px solid rgba(96, 165, 250, 0.15);
  padding-bottom: 1rem;
}

.modern-project-url {
  border-top: 1px solid rgba(96, 165, 250, 0.15);
  padding-top: 1rem;
  margin-top: auto;
}

/* Enhanced hover states for better UX */
.modern-project-card:hover .modern-project-header {
  border-bottom-color: rgba(96, 165, 250, 0.25);
}

.modern-project-card:hover .modern-project-url {
  border-top-color: rgba(96, 165, 250, 0.25);
}

/* Responsive Design */
@media (max-width: 768px) {
  .modern-projects {
    padding: 3rem 0;
  }

  .modern-projects-title {
    font-size: 2.5rem;
  }

  .modern-project-content {
    padding: 1.25rem;
    gap: 0.75rem;
  }

  .modern-project-title {
    font-size: 1.125rem;
  }

  .modern-project-header {
    flex-wrap: wrap;
    gap: 0.75rem;
  }

  .modern-project-title-section {
    flex: 1;
    min-width: 200px;
  }

  .modern-project-url {
    flex-direction: column;
    align-items: stretch;
    gap: 0.75rem;
  }

  .modern-project-link {
    justify-content: center;
  }

  .modern-project-description-text.editable-field-large {
    min-height: 4rem;
  }
}

@media (max-width: 480px) {
  .modern-project-content {
    padding: 1rem;
  }

  .modern-project-header {
    flex-direction: column;
    align-items: stretch;
  }

  .modern-project-delete-btn {
    align-self: flex-end;
    margin-top: 0.5rem;
  }
}

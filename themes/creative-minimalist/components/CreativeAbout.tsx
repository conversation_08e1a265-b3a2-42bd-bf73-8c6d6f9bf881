"use client";
import { useEditorSafe } from "@/contexts/EditorContext";
import { SectionProps, isGeneralPortfolio, isITPortfolio } from "@/lib/types";
import { EditableText } from "@/components/custom-ui/EditableText";
import { getFieldErrors } from "@/lib/portfolio-validation";
import { useEffect } from "react";

// Helper function to strip HTML tags - consistent server/client rendering
const stripHtml = (html: string): string => {
    if (!html) return '';

    // Use regex to strip HTML tags - works consistently on server and client
    return html
        .replace(/<[^>]*>/g, '') // Remove HTML tags
        .replace(/&nbsp;/g, ' ') // Replace non-breaking spaces
        .replace(/&amp;/g, '&')  // Replace HTML entities
        .replace(/&lt;/g, '<')
        .replace(/&gt;/g, '>')
        .replace(/&quot;/g, '"')
        .replace(/&#39;/g, "'")
        .replace(/\s+/g, ' ')    // Normalize whitespace
        .trim();
};

export function CreativeAbout({ isEditing, serverData }: SectionProps) {
    const context = useEditorSafe();
    const data = isEditing && context ? context.state.formData : serverData!;
    const dispatch = isEditing && context ? context.dispatch : null;
    // Get validation result from EditorContext
    const validationResult = context?.state.validationResult;

    // Get validation errors for fields
    const getFieldError = (fieldName: string) => {
        if (!validationResult || !isEditing) return undefined;
        const errors = getFieldErrors(validationResult, fieldName);
        return errors.length > 0 ? errors[0].message : undefined;
    };

    // Check if qualifications validation error should be shown
    const shouldShowQualificationError = (): boolean => {
        const qualificationError = getFieldError('qualifications');
        if (!qualificationError) return false;

        // Only check qualification fields for portfolio types that have them
        if (isGeneralPortfolio(data) || isITPortfolio(data)) {
            return !!(qualificationError && !data.qualification1 && !data.qualification2);
        }

        return false;
    };

    // Clean existing HTML data on component mount
    useEffect(() => {
        if (isEditing && dispatch && data.bio) {
            const cleanBio = stripHtml(data.bio);
            if (cleanBio !== data.bio) {
                dispatch({ type: 'UPDATE_FIELD', payload: { field: 'bio', value: cleanBio } });
            }
        }
        // Only clean qualifications for portfolio types that have them
        if (isEditing && dispatch && isGeneralPortfolio(data) && data.qualifications) {
            const cleanQualifications = stripHtml(data.qualifications);
            if (cleanQualifications !== data.qualifications) {
                dispatch({ type: 'UPDATE_FIELD', payload: { field: 'qualifications', value: cleanQualifications } });
            }
        }
    }, [isEditing, dispatch, data.bio, isGeneralPortfolio(data) ? data.qualifications : null]);

    const handleUpdate = (field: string, value: string) => {
        if (dispatch) {
            dispatch({ type: 'UPDATE_FIELD', payload: { field: field as any, value } });
        }
    };




    return (
        <section id="about" className="theme-creative-about">
            <div className="theme-creative-about-container">
                <div className="theme-creative-about-header">
                    <h2 className="theme-creative-about-title">About Me</h2>
                    <div className="theme-creative-about-divider"></div>
                </div>

                <div className="theme-creative-about-layout">
                    <div className="theme-creative-about-description">
                        <h3 className="theme-creative-about-section-title">Who I Am</h3>
                        <EditableText
                            isEditing={isEditing}
                            tagName="p"
                            className={`theme-creative-about-text ${isEditing ? 'editable-field-large editable-field-creative' : ''}`}
                            initialValue={data.bio || ""}
                            placeholder="Tell us about yourself"
                            hasError={!!getFieldError('bio')}
                            errorMessage={getFieldError('bio')}
                            onSave={(value) => handleUpdate('bio', value)}
                        />
                    </div>

                    {/* Show qualifications section only for portfolio types that have qualification fields */}
                    {(isGeneralPortfolio(data) || isITPortfolio(data)) && (isEditing || data.qualification1 || data.qualification2) && (
                        <div className="theme-creative-about-qualifications">
                            <h3 className="theme-creative-about-section-title">My Qualifications</h3>
                            <div className="theme-creative-about-qualifications-list">
                            {/* Show qualification1 if it has content OR if we're in editing mode */}
                            {(isEditing || data.qualification1) && (
                                <div className="theme-creative-about-qualification-item">
                                    <span className="theme-creative-about-qualification-bullet">•</span>
                                    <EditableText
                                        isEditing={isEditing}
                                        tagName="span"
                                        className={`theme-creative-about-qualification-text ${isEditing ? 'editable-field editable-field-creative' : ''}`}
                                        initialValue={data.qualification1 ||""}
                                        placeholder="e.g., Bachelor's in Computer Science"
                                        hasError={shouldShowQualificationError()}
                                        errorMessage={shouldShowQualificationError() ? getFieldError('qualifications') : undefined}
                                        onSave={(value) => handleUpdate('qualification1', value)}
                                    />
                                </div>
                            )}

                            {/* Show qualification2 if it has content OR if we're in editing mode */}
                            {(isEditing || data.qualification2) && (
                                <div className="theme-creative-about-qualification-item">
                                    <span className="theme-creative-about-qualification-bullet">•</span>
                                    <EditableText
                                        isEditing={isEditing}
                                        tagName="span"
                                        className={`theme-creative-about-qualification-text ${isEditing ? 'editable-field editable-field-creative' : ''}`}
                                        initialValue={data.qualification2 || ""}
                                        placeholder="e.g., 5+ years experience or certification"
                                        hasError={shouldShowQualificationError()}
                                        errorMessage={shouldShowQualificationError() && !data.qualification1 ? getFieldError('qualifications') : undefined}
                                        onSave={(value) => handleUpdate('qualification2', value)}
                                    />
                                </div>
                            )}

                            </div>
                        </div>
                    )}
                </div>
            </div>

        </section>
    );
};

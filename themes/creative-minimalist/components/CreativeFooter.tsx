"use client";
import { useEditorSafe } from "@/contexts/EditorContext";
import { SectionProps } from "@/lib/types";

export function CreativeFooter({ isEditing, serverData }: SectionProps) {
    const context = useEditorSafe();
    const data = isEditing && context ? context.state.formData : serverData!;
    const currentYear = new Date().getFullYear();


    return (
        <footer className="footer-section">
            <div className="footer-container">
                {/* Decorative top border */}
                <div className="footer-border"></div>

                <div className="footer-content">
                    {/* Main footer content */}
                    <div className="footer-main">
                        <div className="footer-brand">
                            <h3 className="footer-name">{data.userName || "Portfolio"}</h3>
                            <p className="footer-tagline">
                                {data.profession ? `${data.profession} • ` : ""}
                                Creating digital experiences with passion
                            </p>

                        </div>

                        {/* <div className="footer-actions">
                            {isExport ? (
                                <a
                                    href="#top"
                                    className="footer-scroll-top"
                                    aria-label="Scroll to top"
                                    title="Back to top"
                                >
                                    <ArrowUp className="footer-scroll-icon" />
                                    <span className="footer-scroll-text">Top</span>
                                </a>
                            ) : (
                                <button
                                    onClick={scrollToTop}
                                    className="footer-scroll-top"
                                    aria-label="Scroll to top"
                                    title="Back to top"
                                >
                                    <ArrowUp className="footer-scroll-icon" />
                                    <span className="footer-scroll-text">Top</span>
                                </button>
                            )}
                        </div> */}
                    </div>

                    {/* Footer bottom */}
                    <div className="footer-bottom">
                        <div className="footer-info">
                            <p className="footer-copyright">
                                © {currentYear} {data.userName || "Portfolio"}. All rights reserved.
                            </p>

                        </div>

                        <div className="footer-attribution">
                            <span className="footer-powered">Powered by</span>
                            <a className="footer-brand-name" href="https://profolify.vercel.app/" target="__blank"><img width="24px" height="24px" src="/icon.png"></img> Profolify
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </footer>
    );
};

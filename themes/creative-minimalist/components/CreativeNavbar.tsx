"use client";
import { useEditorSafe } from "@/contexts/EditorContext";
import { useIsExport } from "@/contexts/ExportContext";
import { SectionProps } from "@/lib/types";
import { useState } from "react";

const navLinks = [
    { href: '#about', label: 'About' },
    { href: '#projects', label: 'Work' },
    { href: '#contact', label: 'Contact' },
];

export function CreativeNavbar({ isEditing, serverData }: SectionProps) {
    const context = useEditorSafe();
    const isExport = useIsExport();
    const data = isEditing && context ? context.state.formData : serverData!;
    const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

    const NavLinksList = ({ isMobile = false, onClick }: { isMobile?: boolean; onClick?: () => void }) => (
        <>
            {navLinks.map(link => (
                <a 
                    key={link.href} 
                    href={link.href} 
                    onClick={onClick}
                    className={isMobile ? "theme-creative-navbar-mobile-link" : "theme-creative-navbar-link"}
                >
                    {link.label}
                </a>
            ))}
        </>
    );

    return (
        <header className="theme-navbar theme-creative-navbar" >
            <div className="theme-creative-navbar-container">
                <a href="#top" className="theme-creative-navbar-brand">
                    {data.userName || "Portfolio"}
                </a>

                {/* Desktop Nav */}
                <nav className="theme-creative-navbar-nav">
                    <NavLinksList />
                </nav>

                {/* Mobile Menu Button - Works for both live and export */}
                <div className="theme-creative-navbar-mobile-toggle">
                    <button
                        type="button"
                        onClick={isExport ? undefined : () => setIsMobileMenuOpen(!isMobileMenuOpen)}
                        aria-label="Toggle menu"
                        className="theme-creative-navbar-mobile-btn"
                        {...(isExport && {
                            'data-mobile-menu-toggle': 'true',
                            'data-target': 'creative-mobile-menu'
                        })}
                    >
                        <svg className="theme-creative-navbar-mobile-icon" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                        </svg>
                    </button>
                </div>
            </div>

            {/* Mobile Menu Dropdown - Works for both live and export */}
            <div
                id="creative-mobile-menu"
                className={`theme-creative-navbar-mobile-menu ${!isExport && isMobileMenuOpen ? 'active' : ''}`}
                style={isExport ? { display: 'none' } : undefined}
            >
                <nav className="theme-creative-navbar-mobile-nav">
                    <NavLinksList
                        isMobile={true}
                        onClick={isExport ? undefined : () => setIsMobileMenuOpen(false)}
                    />
                </nav>
            </div>
        </header>
    );
};

"use client";
import { useEditorSafe } from "@/contexts/EditorContext";
import { SectionProps } from "@/lib/types";
import { EditableText } from "@/components/custom-ui/EditableText";
import { PortfolioData } from "@/lib/types";
import { Mail, Phone, Github, Linkedin, Twitter } from "lucide-react";
import { useAuth } from "@/contexts/AuthenticationContext";
import { getFieldErrors } from "@/lib/portfolio-validation";


export function CreativeContact({ isEditing, serverData }: SectionProps) {
    const context = useEditorSafe();
    const { user } = useAuth();
    const data = isEditing && context ? context.state.formData : serverData!;
    const dispatch = isEditing && context ? context.dispatch : null;
    const validationResult = context?.state.validationResult;

    // Use user's email as default if no email is set
    const defaultEmail = user?.email || '<EMAIL>';

    const handleUpdate = (field: keyof Omit<PortfolioData, 'projects' | 'experiences'>, value: string) => {
        if (dispatch) {
            dispatch({ type: 'UPDATE_FIELD', payload: { field, value } });
        }
    };

    // Get validation errors for fields
    const getFieldError = (fieldName: string) => {
        if (!validationResult || !isEditing) return undefined;
        const errors = getFieldErrors(validationResult, fieldName);
        return errors.length > 0 ? errors[0].message : undefined;
    };

    const contactItems = [
        {
            icon: Mail,
            label: "Email",
            value: data.email || defaultEmail,
            field: 'email' as keyof Omit<PortfolioData, 'projects' | 'experiences'>,
            href: `mailto:${data.email || defaultEmail}`,
            color: "contact-email"
        },
        {
            icon: Phone,
            label: "Phone",
            value: data.phone,
            field: 'phone' as keyof Omit<PortfolioData, 'projects' | 'experiences'>,
            href: `tel:${data.phone}`,
            color: "contact-phone"
        }
    ];

    const socialLinks = [
        {
            icon: Github,
            url: data.githubUrl,
            field: 'githubUrl' as keyof Omit<PortfolioData, 'projects' | 'experiences'>,
            label: "GitHub",
            color: "social-github"
        },
        {
            icon: Linkedin,
            url: data.linkedinUrl,
            field: 'linkedinUrl' as keyof Omit<PortfolioData, 'projects' | 'experiences'>,
            label: "LinkedIn",
            color: "social-linkedin"
        },
        {
            icon: Twitter,
            url: data.twitterUrl,
            field: 'twitterUrl' as keyof Omit<PortfolioData, 'projects' | 'experiences'>,
            label: "Twitter",
            color: "social-twitter"
        }
    ];

    // Always show contact section - email is always available from auth

    return (
        <section id="contact" className="contact-section">
            <div className="contact-container">
                <div className="contact-header">
                    <h2 className="contact-title">Let&#39;s Connect</h2>
                    <p className="contact-subtitle">
                        Ready to bring your ideas to life? Let&#39;s discuss your next project and create something amazing together.
                    </p>
                </div>

                <div className="contact-content">
                    {/* Contact Information */}
                    <div className="contact-info">
                        <h3 className="contact-info-title">Get In Touch</h3>
                        <div className="contact-items">
                            {contactItems.map((item) => {
                                const IconComponent = item.icon;
                                // Always show email, only show phone if it has a value or is being edited
                                if (item.field === 'phone' && !item.value && !isEditing) return null;

                                return (
                                    <div key={item.field} className={`contact-item ${item.color}`}>
                                        <div className="contact-item-icon">
                                            <IconComponent className="contact-icon" />
                                        </div>
                                        <div className="contact-item-content">
                                            <span className="contact-label">{item.label}</span>
                                            {isEditing ? (
                                                <EditableText
                                                    isEditing={isEditing}
                                                    tagName="div"
                                                    className="contact-value-editable"
                                                    initialValue={item.value || ''}
                                                    placeholder={`Enter your ${item.label.toLowerCase()}`}
                                                    hasError={!!getFieldError(item.field)}
                                                    errorMessage={getFieldError(item.field)}
                                                    onSave={(value) => handleUpdate(item.field, value)}
                                                />
                                            ) : (
                                                item.href ? (
                                                    <a href={item.href} className="contact-value-link">
                                                        {item.value}
                                                    </a>
                                                ) : (
                                                    <span className="contact-value">{item.value}</span>
                                                )
                                            )}
                                        </div>
                                    </div>
                                );
                            })}
                        </div>
                    </div>

                    {/* Social Links - only show if there are social links or editing */}
                    {(isEditing || socialLinks.some(link => link.url)) && (
                        <div className="social-section">
                            <h3 className="social-title">Follow Me</h3>
                            <div className="social-grid">
                                {socialLinks.map((social) => {
                                    const IconComponent = social.icon;
                                    if (!social.url && !isEditing) return null;

                                    return (
                                    <div key={social.field} className={`social-item ${social.color}`}>
                                        {isEditing ? (
                                            <div className="social-edit">
                                                <div className="social-icon-wrapper">
                                                    <IconComponent className="social-icon" />
                                                </div>
                                                <div className="social-edit-content">
                                                    <span className="social-label">{social.label}</span>
                                                    <EditableText
                                                        isEditing={isEditing}
                                                        tagName="div"
                                                        className="social-url-editable"
                                                        initialValue={social.url || ''}
                                                        placeholder={`https://${social.label.toLowerCase()}.com/username`}
                                                        hasError={!!getFieldError(social.field)}
                                                        errorMessage={getFieldError(social.field)}
                                                        onSave={(value) => handleUpdate(social.field, value)}
                                                    />
                                                </div>
                                            </div>
                                        ) : (
                                            <a
                                                href={social.url}
                                                target="_blank"
                                                rel="noopener noreferrer"
                                                className="social-link"
                                                title={`Follow me on ${social.label}`}
                                            >
                                                <IconComponent className="social-icon" />
                                                <span className="social-label">{social.label}</span>
                                            </a>
                                        )}
                                    </div>
                                );
                            })}
                        </div>
                    </div>
                    )}
                </div>
            </div>
        </section>
    );
};

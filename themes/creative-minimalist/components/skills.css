/* Creative Skills Section */
.skills-section {
  padding: 5rem 0;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  position: relative;
  overflow: hidden;
}

.skills-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.1) 0%, transparent 50%);
  pointer-events: none;
}

.skills-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  position: relative;
  z-index: 1;
}

.skills-header {
  text-align: center;
  margin-bottom: 4rem;
}

.skills-title {
  font-size: 3rem;
  font-weight: 700;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 1rem;
  line-height: 1.2;
}

.skills-subtitle {
  font-size: 1.25rem;
  color: #64748b;
  font-weight: 400;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

.skills-content {
  display: flex;
  flex-direction: column;
  gap: 3rem;
}

/* Skills Badges Grid */
.skills-badges-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  justify-content: center;
  align-items: flex-start;
}

/* Individual Skill Badge */
.skill-badge {
  position: relative;
  display: inline-flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 2px solid transparent;
  border-radius: 2rem;
  padding: 0.75rem 1.5rem;
  font-size: 0.95rem;
  font-weight: 600;
  color: #1e293b;
  transition: all 0.3s ease;
  box-shadow: 
    0 4px 15px rgba(0, 0, 0, 0.08),
    0 2px 6px rgba(0, 0, 0, 0.04);
  cursor: default;
  overflow: hidden;
}

.skill-badge::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, 
    rgba(102, 126, 234, 0.1) 0%, 
    rgba(118, 75, 162, 0.1) 50%,
    rgba(255, 119, 198, 0.1) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: -1;
}

.skill-badge:hover::before {
  opacity: 1;
}

.skill-badge:hover {
  transform: translateY(-3px) scale(1.05);
  border-color: rgba(102, 126, 234, 0.3);
  box-shadow: 
    0 10px 30px rgba(102, 126, 234, 0.2),
    0 4px 15px rgba(0, 0, 0, 0.1);
}

.skill-badge-text {
  position: relative;
  z-index: 1;
}

/* Editing Mode Styles */
.skill-badge-edit {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  width: 100%;
}

.skill-badge-input {
  flex: 1;
  background: transparent;
  border: none;
  outline: none;
  font-size: 0.95rem;
  font-weight: 600;
  color: #1e293b;
  padding: 0;
  min-width: 80px;
}

.skill-badge-input::placeholder {
  color: #9ca3af;
  font-style: italic;
}

.skill-badge-delete {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 1.5rem;
  height: 1.5rem;
  background: rgba(239, 68, 68, 0.1);
  color: #dc2626;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.skill-badge-delete:hover {
  background: rgba(239, 68, 68, 0.2);
  transform: scale(1.1);
}

.skill-badge-delete-icon {
  width: 0.75rem;
  height: 0.75rem;
}

/* Editing state styling */
.skill-badge:has(.skill-badge-edit) {
  background: rgba(102, 126, 234, 0.05);
  border-color: rgba(102, 126, 234, 0.3);
  border-style: dashed;
}

.skill-badge:has(.skill-badge-edit):hover {
  background: rgba(102, 126, 234, 0.1);
  border-color: rgba(102, 126, 234, 0.5);
}

/* Add Button */
.skills-add-container {
  display: flex;
  justify-content: center;
  margin-top: 2rem;
}

.skills-add-btn {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem 2rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 1rem;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.skills-add-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.skills-add-icon {
  width: 1.25rem;
  height: 1.25rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .skills-section {
    padding: 3rem 0;
  }
  
  .skills-container {
    padding: 0 1rem;
  }
  
  .skills-title {
    font-size: 2rem;
  }
  
  .skills-subtitle {
    font-size: 1rem;
  }
  
  .skills-badges-grid {
    gap: 0.75rem;
  }
  
  .skill-badge {
    padding: 0.6rem 1.2rem;
    font-size: 0.9rem;
  }
}

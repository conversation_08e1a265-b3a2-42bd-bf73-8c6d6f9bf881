/* Modern Projects Section */
.projects-section {
  padding: 5rem 1rem;
  background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
  position: relative;
  overflow: hidden;
}

.projects-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 30% 70%, rgba(59, 130, 246, 0.03) 0%, transparent 50%),
              radial-gradient(circle at 70% 30%, rgba(99, 102, 241, 0.03) 0%, transparent 50%);
  pointer-events: none;
}

@media (min-width: 768px) {
  .projects-section {
    padding: 6rem 1rem;
  }
}

.projects-container {
  max-width: 1200px;
  margin: 0 auto;
  position: relative;
  z-index: 1;
  padding: 1rem;
}

.projects-header {
  text-align: center;
  margin-bottom: 4rem;
}

@media (min-width: 768px) {
  .projects-header {
    margin-bottom: 5rem;
  }
}

.projects-title {
  font-size: 2.5rem;
  font-weight: 800;
  color: #1e293b;
  margin-bottom: 1rem;
  position: relative;
  background: linear-gradient(135deg, #1e293b 0%, #3b82f6 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  letter-spacing: -0.02em;
}

@media (min-width: 640px) {
  .projects-title {
    font-size: 3rem;
  }
}

@media (min-width: 768px) {
  .projects-title {
    font-size: 3.5rem;
  }
}

.projects-title::after {
  content: '';
  position: absolute;
  bottom: -0.75rem;
  left: 50%;
  transform: translateX(-50%);
  width: 5rem;
  height: 4px;
  background: linear-gradient(90deg, #3b82f6, #6366f1);
  border-radius: 2px;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

@media (min-width: 480px) {
  .projects-title::after {
    width: 6rem;
  }
}

.projects-subtitle {
  font-size: 1.125rem;
  color: #64748b;
  font-weight: 500;
  max-width: 700px;
  margin: 0 auto;
  line-height: 1.6;
}

@media (min-width: 768px) {
  .projects-subtitle {
    font-size: 1.25rem;
  }
}

.projects-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 2rem;
}

@media (min-width: 768px) {
  .projects-grid {
    grid-template-columns: repeat(auto-fit, minmax(380px, 1fr));
    gap: 2.5rem;
  }
}

@media (min-width: 1024px) {
  .projects-grid {
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 3rem;
  }
}

/* Project Card */
.project-card {
  background: rgba(255, 255, 255, 0.8);
  border-radius: 1.25rem;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
  transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
  border: 1px solid rgba(226, 232, 240, 0.8);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  height: 100%;
  display: flex;
  flex-direction: column;
}

.project-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.12);
  border-color: rgba(203, 213, 225, 0.8);
}

/* Project Image */
.project-image-container {
  position: relative;
  width: 100%;
  height: 240px;
  overflow: hidden;
  background: #f1f5f9;
}

@media (min-width: 768px) {
  .project-image-container {
    height: 260px;
  }
}

.project-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.project-card:hover .project-image {
  transform: scale(1.05);
}

/* Upload Overlay */
.project-upload-overlay {
  position: absolute;
  inset: 0;
  background: rgba(15, 23, 42, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
  color: white;
  font-weight: 600;
  cursor: pointer;
  z-index: 10;
}

/* Show upload overlay when hovering or when uploading */
.project-upload-overlay:hover,
.project-upload-overlay.uploading {
  opacity: 1;
}

.project-upload-input {
  position: absolute;
  inset: 0;
  opacity: 0;
  cursor: pointer;
  width: 100%;
  height: 100%;
  z-index: 20;
}

.project-upload-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.75rem;
  padding: 1.5rem;
  text-align: center;
}

.project-upload-icon {
  width: 2rem;
  height: 2rem;
  color: white;
  opacity: 0.9;
}

.project-upload-text {
  font-size: 1rem;
  font-weight: 600;
  color: white;
  opacity: 0.9;
}

.project-upload-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.75rem;
}

.project-upload-spinner {
  width: 2rem;
  height: 2rem;
  color: white;
  animation: spin 1.5s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Project Overlay */
.project-overlay {
  position: absolute;
  inset: 0;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.8) 0%, rgba(0, 0, 0, 0) 60%);
  display: flex;
  align-items: flex-end;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.project-card:hover .project-overlay {
  opacity: 1;
}

.project-overlay-content {
  padding: 1.5rem;
  width: 100%;
}

.project-overlay-title {
  font-size: 1.125rem;
  font-weight: 700;
  color: white;
  margin-bottom: 0.75rem;
  line-height: 1.3;
}

.project-overlay-links {
  display: flex;
  gap: 0.75rem;
}

.project-overlay-link {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.5rem;
  height: 2.5rem;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 0.5rem;
  color: white;
  text-decoration: none;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.project-overlay-link:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
}

.project-overlay-link-icon {
  width: 1.125rem;
  height: 1.125rem;
}

/* Project Content */
.project-content {
  padding: 1.75rem;
  flex: 1;
  display: flex;
  flex-direction: column;
}

@media (min-width: 768px) {
  .project-content {
    padding: 2rem;
  }
}

.project-title {
  font-size: 1.375rem;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 1rem;
  line-height: 1.3;
  letter-spacing: -0.01em;
}

.project-description {
  color: #64748b;
  line-height: 1.6;
  margin-bottom: 1.5rem;
  flex: 1;
  font-size: 0.95rem;
}

/* Editable Field Styles */
.project-title[contenteditable="true"],
.project-description[contenteditable="true"],
.project-url-field[contenteditable="true"] {
  border: 2px dashed rgba(59, 130, 246, 0.3);
  border-radius: 0.5rem;
  padding: 0.75rem;
  background: rgba(59, 130, 246, 0.05);
  transition: all 0.3s ease;
  min-height: 2rem;
}

.project-title[contenteditable="true"]:hover,
.project-description[contenteditable="true"]:hover,
.project-url-field[contenteditable="true"]:hover {
  border-color: rgba(59, 130, 246, 0.5);
  background: rgba(59, 130, 246, 0.08);
}

.project-title[contenteditable="true"]:focus,
.project-description[contenteditable="true"]:focus,
.project-url-field[contenteditable="true"]:focus {
  outline: none;
  border-color: #3b82f6;
  background: rgba(59, 130, 246, 0.1);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.project-title[contenteditable="true"]:empty:before,
.project-description[contenteditable="true"]:empty:before,
.project-url-field[contenteditable="true"]:empty:before {
  content: attr(data-placeholder);
  color: #94a3b8;
  font-style: italic;
}

.project-edit-fields {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid rgba(226, 232, 240, 0.6);
}

.project-url-field {
  font-size: 0.875rem;
  color: #64748b;
  min-height: 2.5rem;
  display: flex;
  align-items: center;
}

.project-remove-btn {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  color: white;
  border: none;
  padding: 0.75rem 1rem;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  box-shadow: 0 2px 8px rgba(239, 68, 68, 0.2);
}

.project-remove-btn:hover {
  background: linear-gradient(135deg, #dc2626, #b91c1c);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
}

.project-remove-icon {
  width: 0.875rem;
  height: 0.875rem;
}

/* Add Project Card */
.project-add-card {
  background: rgba(255, 255, 255, 0.6);
  border: 2px dashed #cbd5e1;
  border-radius: 1.25rem;
  padding: 3rem 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  min-height: 300px;
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
}

.project-add-card:hover {
  background: rgba(255, 255, 255, 0.8);
  border-color: #3b82f6;
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(59, 130, 246, 0.1);
}

.project-add-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  text-align: center;
}

.project-add-icon {
  width: 3rem;
  height: 3rem;
  color: #64748b;
  transition: all 0.3s ease;
}

.project-add-card:hover .project-add-icon {
  color: #3b82f6;
  transform: scale(1.1);
}

.project-add-text {
  font-size: 1.125rem;
  font-weight: 600;
  color: #64748b;
  transition: all 0.3s ease;
}

.project-add-card:hover .project-add-text {
  color: #3b82f6;
}

/* Mobile-specific improvements */
@media (max-width: 479px) {
  .projects-section {
    padding: 3rem 0.75rem;
  }

  .projects-title {
    font-size: 2rem;
  }

  .projects-header {
    margin-bottom: 2.5rem;
  }

  .projects-grid {
    gap: 1.5rem;
  }

  .project-image-container {
    height: 200px;
  }

  .project-content {
    padding: 1.25rem;
  }

  .project-add-card {
    padding: 2rem 1rem;
    min-height: 200px;
  }

  .project-add-icon {
    width: 2.5rem;
    height: 2.5rem;
  }

  .project-add-text {
    font-size: 1rem;
  }
}

/* Print styles */
@media print {
  .projects-section {
    background: white;
  }

  .project-card {
    break-inside: avoid;
    margin-bottom: 1rem;
    background: white;
    border: 1px solid #e2e8f0;
  }

  .project-upload-overlay,
  .project-overlay,
  .project-edit-fields,
  .project-add-card {
    display: none;
  }
}

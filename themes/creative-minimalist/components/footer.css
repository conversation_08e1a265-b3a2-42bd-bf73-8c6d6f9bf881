/* Modern Footer Section */
.footer-section {
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
  color: #e2e8f0;
  position: relative;
  overflow: hidden;
}

.footer-section::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(
      circle at 30% 80%,
      rgba(59, 130, 246, 0.1) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 70% 20%,
      rgba(99, 102, 241, 0.08) 0%,
      transparent 50%
    );
  pointer-events: none;
}

.footer-container {
  max-width: 1200px;
  margin: 0 auto;
  position: relative;
  z-index: 1;
}

.footer-border {
  height: 4px;
  background: linear-gradient(
    90deg,
    transparent 0%,
    #3b82f6 20%,
    #6366f1 50%,
    #8b5cf6 80%,
    transparent 100%
  );
  margin-bottom: 3rem;
  border-radius: 2px;
}

.footer-content {
  padding: 0 1rem 3rem;
  display: flex;
  flex-direction: column;
  gap: 3rem;
}

@media (min-width: 768px) {
  .footer-content {
    padding: 0 1rem 4rem;
  }
}

.footer-main {
  display: flex;
  flex-direction: column;
  gap: 2.5rem;
  align-items: center;
  text-align: center;
}

@media (min-width: 768px) {
  .footer-main {
    flex-direction: row;
    justify-content: space-between;
    align-items: flex-start;
    text-align: left;
  }
}

.footer-brand {
  flex: 1;
  max-width: 600px;
}

.footer-name {
  font-size: 2rem;
  font-weight: 800;
  color: white;
  margin-bottom: 0.75rem;
  background: linear-gradient(135deg, #ffffff 0%, #e2e8f0 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  letter-spacing: -0.02em;
}

@media (min-width: 768px) {
  .footer-name {
    font-size: 2.25rem;
  }
}

.footer-tagline {
  color: #94a3b8;
  font-size: 1rem;
  line-height: 1.6;
  margin-bottom: 1.5rem;
  font-weight: 400;
}

@media (min-width: 768px) {
  .footer-tagline {
    font-size: 1.125rem;
  }
}

/* Footer Stats */
.footer-stats {
  display: flex;
  flex-wrap: wrap;
  gap: 1.5rem;
  justify-content: center;
}

@media (min-width: 768px) {
  .footer-stats {
    justify-content: flex-start;
  }
}

.footer-stat {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 2rem;
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.footer-stat:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-2px);
}

.footer-stat-icon {
  width: 1rem;
  height: 1rem;
  color: #60a5fa;
}

.footer-stat-text {
  font-size: 0.875rem;
  font-weight: 500;
  color: #cbd5e1;
}

/* Footer Actions */
.footer-actions {
  display: flex;
  align-items: center;
  justify-content: center;
}

.footer-scroll-top {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 0.25rem;
  padding: 1rem;
  background: rgba(59, 130, 246, 0.1);
  color: #60a5fa;
  border: 2px solid rgba(59, 130, 246, 0.2);
  border-radius: 1rem;
  font-size: 0.75rem;
  font-weight: 600;
  transition: all 0.3s ease;
  cursor: pointer;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  text-decoration: none;
}

.footer-scroll-top:hover {
  background: rgba(59, 130, 246, 0.2);
  border-color: rgba(59, 130, 246, 0.4);
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(59, 130, 246, 0.2);
}

.footer-scroll-icon {
  width: 1.25rem;
  height: 1.25rem;
  transition: transform 0.3s ease;
}

.footer-scroll-top:hover .footer-scroll-icon {
  transform: translateY(-2px);
}

.footer-scroll-text {
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* Footer Bottom */
.footer-bottom {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  align-items: center;
  padding-top: 2rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  text-align: center;
}

@media (min-width: 768px) {
  .footer-bottom {
    flex-direction: row;
    justify-content: space-between;
    text-align: left;
  }
}

.footer-info {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  align-items: center;
}

@media (min-width: 768px) {
  .footer-info {
    align-items: flex-start;
  }
}

.footer-copyright {
  color: #94a3b8;
  font-size: 0.875rem;
  font-weight: 400;
}

.footer-made-with {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #94a3b8;
  font-size: 0.875rem;
}

.footer-made-text {
  font-weight: 400;
}

.footer-heart {
  width: 1rem;
  height: 1rem;
  color: #ef4444;
  animation: heartbeat 2s ease-in-out infinite;
}

@keyframes heartbeat {
  0%,
  50%,
  100% {
    transform: scale(1);
  }
  25%,
  75% {
    transform: scale(1.2);
  }
}

.footer-sparkles {
  width: 1rem;
  height: 1rem;
  color: #fbbf24;
  animation: sparkle 3s ease-in-out infinite;
}

@keyframes sparkle {
  0%,
  100% {
    transform: rotate(0deg) scale(1);
    opacity: 0.8;
  }
  50% {
    transform: rotate(180deg) scale(1.1);
    opacity: 1;
  }
}

/* Footer Attribution */
.footer-attribution {
  display: flex; /* Ensure it's a flex container */
  flex-direction: row; /* Ensure items are in a row */
  align-items: center; /* Vertically center items */
  gap: 0.5rem;
  font-size: 0.875rem;
}

/* Hide 'Powered by' section in exported version */
[data-export] .footer-attribution {
  display: none !important;
}

@media (min-width: 768px) {
  .footer-attribution {
    align-items: flex-end;
  }
}
.footer-powered {
  color: #64748b;
  font-weight: 400;
}
.footer-brand-name {
  color: #3b82f6;
  font-weight: 700;
  background: linear-gradient(135deg, #3b82f6, #6366f1);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  transition: all 0.3s ease;
  /* Add flex properties to align image and text inside the link */
  display: flex;
  align-items: center;
  gap: 0.25rem; /* Adjust gap as needed for icon and text */
}
.footer-brand-name a {
  text-decoration: none;
}
.footer-brand-name:hover {
  background: linear-gradient(135deg, #2563eb, #4f46e5);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Mobile-specific improvements */
@media (max-width: 479px) {
  .footer-content {
    padding: 0 0.75rem 2rem;
  }

  .footer-border {
    margin-bottom: 2rem;
  }

  .footer-name {
    font-size: 1.5rem;
  }

  .footer-tagline {
    font-size: 0.9rem;
  }

  .footer-stats {
    gap: 1rem;
  }

  .footer-stat {
    padding: 0.375rem 0.75rem;
  }

  .footer-stat-text {
    font-size: 0.8rem;
  }

  .footer-scroll-top {
    padding: 0.75rem;
  }

  .footer-scroll-icon {
    width: 1rem;
    height: 1rem;
  }

  .footer-scroll-text {
    font-size: 0.7rem;
  }

  .footer-bottom {
    gap: 1rem;
    padding-top: 1.5rem;
  }

  .footer-copyright,
  .footer-made-with,
  .footer-attribution {
    font-size: 0.8rem;
  }
}

/* Print styles */
@media print {
  .footer-section {
    background: white;
    color: #1f2937;
  }

  .footer-section::before {
    display: none;
  }

  .footer-border {
    background: #e5e7eb;
  }

  .footer-name {
    color: #1f2937;
    background: none;
    -webkit-text-fill-color: #1f2937;
  }

  .footer-brand-name {
    color: #3b82f6;
    background: none;
    -webkit-text-fill-color: #3b82f6;
  }

  .footer-scroll-top,
  .footer-stats {
    display: none;
  }

  .footer-main {
    align-items: center;
    text-align: center;
  }

  .footer-bottom {
    justify-content: center;
    text-align: center;
  }

  .footer-info {
    align-items: center;
  }
}

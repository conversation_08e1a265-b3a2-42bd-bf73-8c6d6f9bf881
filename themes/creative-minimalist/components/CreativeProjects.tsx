"use client";
import { EditableText } from "@/components/custom-ui/EditableText";
import { useEditorSafe } from "@/contexts/EditorContext";
import { SectionProps, isGeneralPortfolio, isITPortfolio } from "@/lib/types";
import { Upload, Trash2, Loader2, Plus } from "lucide-react";
import { PortfolioImage } from "@/components/custom-ui/PortfolioImage";
import { useIsExport } from "@/contexts/ExportContext";
import { Project } from "@/lib/types";
import { getFieldErrors, ValidationResult } from "@/lib/portfolio-validation";

interface ProjectCardProps {
    project: Project;
    index: number;
    isEditing: boolean;
    isUploading: boolean;
    onUpdate: (index: number, field: keyof Project, value: string) => void;
    onImageUpload: (event: React.ChangeEvent<HTMLInputElement>, projectId: string) => void;
    onRemove: (projectId: string) => void;
    isExport: boolean;
    validationResult?: ValidationResult;

}

const ProjectCard = ({ project, index, isEditing, isUploading, onUpdate, onImageUpload, onRemove, validationResult }: ProjectCardProps) => {
    // Get validation errors for this specific project
    const getFieldError = (fieldName: string) => {
        if (!validationResult || !isEditing) return undefined;
        const errors = getFieldErrors(validationResult, `projects[${index}].${fieldName}`);
        return errors.length > 0 ? errors[0].message : undefined;
    };
    return (
        <div className="project-card">
            {/* Project Image */}
            <div className="project-image-container">
                <PortfolioImage
                    isEditing={isEditing}
                    src={project.imageUrl || 'https://placehold.co/600x400/f8fafc/64748b?text=Project+Image'}
                    alt={project.title}
                    width={600}
                    height={400}
                    className="project-image"
                />

                {/* Upload Overlay */}
                {isEditing && (
                    <label className={`project-upload-overlay ${isUploading ? 'uploading' : ''}`}>
                        <input
                            type="file"
                            accept="image/*"
                            onChange={(e) => onImageUpload(e, project.id)}
                            className="project-upload-input"
                            disabled={isUploading}
                        />
                        {isUploading ? (
                            <div className="project-upload-loading">
                                <Loader2 className="project-upload-spinner" />
                                <span className="project-upload-text">Uploading...</span>
                            </div>
                        ) : (
                            <div className="project-upload-content">
                                <Upload className="project-upload-icon" />
                                <span className="project-upload-text">Upload Image</span>
                            </div>
                        )}
                    </label>
                )}

                {/* Project Overlay Info */}
                <div className="project-overlay">
                    <div className="project-overlay-content">
                        <h3 className="project-overlay-title">{project.title || 'Project Title'}</h3>
                    </div>
                </div>
            </div>

            {/* Project Content */}
            <div className="project-content">
                <EditableText
                    isEditing={isEditing}
                    tagName="h3"
                    className="project-title"
                    initialValue={project.title}
                    placeholder="Project Title"
                    hasError={!!getFieldError('title')}
                    errorMessage={getFieldError('title')}
                    onSave={(value) => onUpdate(index, 'title', value)}
                />
                <EditableText
                    isEditing={isEditing}
                    tagName="p"
                    className="project-description"
                    initialValue={project.description}
                    placeholder="Brief description of your project, technologies used, and key features..."
                    hasError={!!getFieldError('description')}
                    errorMessage={getFieldError('description')}
                    onSave={(value) => onUpdate(index, 'description', value)}
                />

                {isEditing && (
                    <div className="project-edit-fields">

                        <button
                            onClick={() => onRemove(project.id)}
                            className="project-remove-btn"
                            title="Remove project"
                        >
                            <Trash2 className="project-remove-icon" />
                            Remove Project
                        </button>
                    </div>
                )}
            </div>
        </div>
    );
};

export function CreativeProjects({ isEditing, serverData, onImageUpload }: SectionProps) {
    const context = useEditorSafe();
    const isExport = useIsExport();

    const data = isEditing && context ? context.state.formData : serverData!;
    const dispatch = isEditing && context ? context.dispatch : null;
    const validationResult = context?.state.validationResult;


    const handleUpdate = (index: number, field: keyof Project, value: string) => {
        if (dispatch) {
            dispatch({ type: 'UPDATE_PROJECT', payload: { index, field, value } });
        }
    };

    const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>, projectId: string) => {
        const file = event.target.files?.[0];
        if (file && onImageUpload) {
            onImageUpload({ file, type: 'project', id: projectId });
        }
    };

    const removeProject = (projectId: string) => {
        if (dispatch) {
            dispatch({ type: 'DELETE_PROJECT', payload: { id: projectId } });
        }
    };

    const addProject = () => {
        if (dispatch) {
            // If we only have the default project, replace it with a new one
            if (isGeneralPortfolio(data) || isITPortfolio(data)) {
                if (data.projects.length === 0) {
                    dispatch({ type: 'ADD_PROJECT' });
                } else {
                    dispatch({ type: 'ADD_PROJECT' });
                }
            }
        }
    };

    // Only show projects section for portfolio types that support projects
    if (!isGeneralPortfolio(data) && !isITPortfolio(data)) {
        return null;
    }

    const projects = data.projects || [];

    return (
        <section id="projects" className="projects-section">
            <div className="projects-container">
                <div className="projects-header">
                    <h2 className="projects-title">Featured Projects</h2>
                    <p className="projects-subtitle">
                        A showcase of projects that demonstrate creativity, technical expertise, and innovative solutions
                    </p>
                </div>

                <div className="projects-grid">
                    {projects.map((project, index) => (
                        <ProjectCard
                            key={project.id}
                            project={project}
                            index={index}
                            isEditing={isEditing}
                            isUploading={isEditing && context?.state.isUploading?.type === 'project' && context.state.isUploading.id === project.id}
                            onUpdate={handleUpdate}
                            onImageUpload={handleImageUpload}
                            onRemove={removeProject}
                            isExport={isExport}
                            validationResult={validationResult}
                        />
                    ))}

                    {isEditing && (
                        <div className="project-add-card" onClick={addProject}>
                            <div className="project-add-content">
                                <Plus className="project-add-icon" />
                                <span className="project-add-text">Add New Project</span>
                            </div>
                        </div>
                    )}
                </div>
            </div>
        </section>
    );
};

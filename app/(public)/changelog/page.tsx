"use client";

import { Badge } from "@/components/ui/badge";
import { ArrowLeft, Calendar, Sparkles, Shield, Users, Zap, Star, Gift, CheckCircle, Wrench } from "lucide-react";
import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";

// Changelog data structure
const changelogEntries = [
  {
    version: "1.1",
    title: "Quick Improvements",
    date: "July 15, 2025",
    isLatest: true,
    summary: "Quick fixes and improvements based on initial user feedback",
    changes: [
      {
        type: "improvement",
        category: "User Experience",
        icon: Users,
        items: [
          "Added account settings page for better profile management",
          "Enhanced user data handling and privacy protection",
          "Added account deletion option for user control"
        ]
      },
      {
        type: "security",
        category: "Security & Stability",
        icon: Shield,
        items: [
          "Enhanced database security with better user data protection",
          "Improved authentication system reliability",
          "Fixed various bugs and improved error handling",
          "Better data validation and security measures"
        ]
      },
      {
        type: "performance",
        category: "Performance & Bug Fixes",
        icon: Zap,
        items: [
          "Fixed portfolio slug generation for cleaner URLs",
          "Improved loading times and responsiveness",
          "Better error messages and user feedback",
          "Code cleanup and optimization improvements"
        ]
      }
    ]
  },
  {
    version: "1.0",
    title: "Platform Launch",
    date: "July 14, 2025",
    isLatest: false,
    summary: "Initial release with complete portfolio building platform",
    changes: [
      {
        type: "feature",
        category: "Export Your Portfolio",
        icon: Gift,
        items: [
          "Download your portfolio as a complete website",
          "Host anywhere - no dependencies required",
          "Perfect pixel-by-pixel copy of your live site",
          "Mobile-responsive exported sites"
        ]
      },
      {
        type: "feature",
        category: "Multiple Themes",
        icon: Sparkles,
        items: [
          "Modern theme for developers and tech professionals",
          "Creative Minimalist theme for designers",
          "Consistent editing experience across themes"
        ]
      },
      {
        type: "feature",
        category: "Core Features",
        icon: Zap,
        items: [
          "Live portfolio editor with real-time preview",
          "Google authentication for easy sign-up",
          "Image upload and optimization",
          "Public portfolio publishing with custom URLs"
        ]
      }
    ]
  }
];

const comingSoonFeatures = [
  {
    title: "Premium Themes",
    description: "Advanced theme designs with premium customization options",
    icon: Star
  },
  {
    title: "Custom Domains",
    description: "Connect your own domain to your portfolio",
    icon: Zap
  }
];

export default function ChangelogPage() {
  return (
    <div className="min-h-screen bg-backgroundPrimary">
      {/* Clean Header */}
      <div className="border-b border-borderPrimary bg-backgroundPrimary sticky top-0 z-10">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Link href="/">
                <Button variant="ghost" size="sm" className="flex items-center gap-2">
                  <ArrowLeft className="w-4 h-4" />
                  Back to Home
                </Button>
              </Link>
              <div className="h-6 w-px bg-borderPrimary"></div>
              <div>
                <h1 className="text-2xl font-bold text-textPrimary">Changelog</h1>
                <p className="text-textSecondary text-sm">Latest updates and improvements</p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Calendar className="w-5 h-5 text-brandPrimary" />
              <span className="text-sm text-textSecondary">Updated regularly</span>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Changelog Entries */}
        <div className="space-y-12">
          {changelogEntries.map((entry, entryIndex) => (
            <div key={entry.version} className="relative">
              {/* Timeline Line */}
              {entryIndex !== changelogEntries.length - 1 && (
                <div className="absolute left-6 top-16 bottom-0 w-px bg-borderPrimary"></div>
              )}

              {/* Entry Header */}
              <div className="flex items-start gap-4 mb-6">
                <div className={`w-12 h-12 rounded-full flex items-center justify-center flex-shrink-0 ${
                  entry.isLatest
                    ? 'bg-brandPrimary text-white'
                    : 'bg-backgroundSecondary text-textSecondary border border-borderPrimary'
                }`}>
                  {entry.isLatest ? (
                    <Sparkles className="w-5 h-5" />
                  ) : (
                    <CheckCircle className="w-5 h-5" />
                  )}
                </div>

                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-3 mb-2">
                    {entry.isLatest && (
                      <Badge className="bg-brandPrimary/10 text-brandPrimary border-brandPrimary/20 hover:bg-brandPrimary/20">
                        Latest
                      </Badge>
                    )}
                    <h2 className="text-xl font-semibold text-textPrimary">
                      Version {entry.version} - {entry.title}
                    </h2>
                    <span className="text-sm text-textSecondary">{entry.date}</span>
                  </div>
                  <p className="text-textSecondary">{entry.summary}</p>
                </div>
              </div>

              {/* Changes */}
              <div className="ml-16 space-y-6">
                {entry.changes.map((changeGroup, groupIndex) => {
                  const IconComponent = changeGroup.icon;
                  const getTypeColor = (type: string) => {
                    switch (type) {
                      case 'feature': return 'text-brandAccent bg-brandAccent/10';
                      case 'improvement': return 'text-brandPrimary bg-brandPrimary/10';
                      case 'security': return 'text-blue-600 bg-blue-50';
                      case 'performance': return 'text-orange-600 bg-orange-50';
                      default: return 'text-textSecondary bg-backgroundSecondary';
                    }
                  };

                  return (
                    <div key={groupIndex} className="bg-backgroundSecondary/50 rounded-lg p-6 border border-borderPrimary">
                      <div className="flex items-center gap-3 mb-4">
                        <div className={`w-8 h-8 rounded-lg flex items-center justify-center ${getTypeColor(changeGroup.type)}`}>
                          <IconComponent className="w-4 h-4" />
                        </div>
                        <h3 className="font-medium text-textPrimary">{changeGroup.category}</h3>
                      </div>
                      <ul className="space-y-2">
                        {changeGroup.items.map((item, itemIndex) => (
                          <li key={itemIndex} className="flex items-start gap-3 text-sm text-textSecondary">
                            <div className="w-1.5 h-1.5 rounded-full bg-brandPrimary/60 mt-2 flex-shrink-0"></div>
                            <span>{item}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  );
                })}
              </div>
            </div>
          ))}
        </div>

        {/* Coming Soon Section */}
        <div className="mt-16 pt-12 border-t border-borderPrimary">
          <div className="text-center mb-8">
            <div className="inline-flex items-center gap-2 px-4 py-2 bg-brandPrimary/10 text-brandPrimary rounded-full text-sm font-medium mb-4">
              <Wrench className="w-4 h-4" />
              In Development
            </div>
            <h2 className="text-2xl font-bold text-textPrimary mb-2">Coming Soon</h2>
            <p className="text-textSecondary">Exciting features we&apos;re working on for future releases</p>
          </div>

          <div className="grid md:grid-cols-2 gap-6 max-w-2xl mx-auto">
            {comingSoonFeatures.map((feature, index) => {
              const IconComponent = feature.icon;
              return (
                <div key={index} className="bg-backgroundSecondary/30 rounded-lg p-6 border border-borderPrimary border-dashed text-center">
                  <div className="w-12 h-12 bg-brandPrimary/10 rounded-lg flex items-center justify-center mx-auto mb-4">
                    <IconComponent className="w-6 h-6 text-brandPrimary" />
                  </div>
                  <h3 className="font-semibold text-textPrimary mb-2">{feature.title}</h3>
                  <p className="text-sm text-textSecondary">{feature.description}</p>
                </div>
              );
            })}
          </div>
        </div>

        {/* Footer CTA */}
        <div className="mt-16 pt-12 border-t border-borderPrimary text-center">
          <div className="max-w-md mx-auto">
            <h3 className="text-lg font-semibold text-textPrimary mb-2">Ready to get started?</h3>
            <p className="text-textSecondary mb-6">
              Create your professional portfolio in minutes with our intuitive editor.
            </p>
            <Link href="/dashboard">
              <Button variant="default" size="lg" className="px-8">
                Start Building Your Portfolio
              </Button>
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}

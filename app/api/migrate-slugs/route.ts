import { NextResponse } from 'next/server';
import { fixTempSlugs } from '@/lib/portfolio-api';

export async function POST() {
  try {
    // Run the migration
    await fixTempSlugs();
    
    return NextResponse.json({ 
      success: true, 
      message: 'Migration completed successfully' 
    });
  } catch (error) {
    console.error('Migration failed:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      },
      { status: 500 }
    );
  }
}

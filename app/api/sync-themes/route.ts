import { NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';

// Theme configuration (matches theme-registry.ts)
const THEMES = [
  {
    id: 'modern-theme-v1',
    name: 'Modern',
    sourceCssFile: 'themes/modern/modern-compiled.css',
    cssFile: '/themes/modern/modern-compiled.css',
  },
  {
    id: 'creative-theme-v1',
    name: 'Creative Minimalist',
    sourceCssFile: 'themes/creative-minimalist/creative-minimalist-compiled.css',
    cssFile: '/themes/creative-minimalist/creative-minimalist-compiled.css',
  },
];

/**
 * Sync CSS files from themes directory to public directory
 */
interface SyncResult {
  theme: string;
  status: 'success' | 'error';
  sourceSize?: number;
  publicSize?: number;
  sourcePath?: string;
  publicPath?: string;
  error?: string;
}

function syncThemeCssFiles(): { success: boolean; results: SyncResult[] } {
  const results: SyncResult[] = [];
  let allSuccess = true;

  for (const theme of THEMES) {
    try {
      const sourcePath = path.join(process.cwd(), theme.sourceCssFile);
      const publicPath = path.join(process.cwd(), 'public', theme.cssFile);
      
      // Ensure public directory exists
      const publicDir = path.dirname(publicPath);
      if (!fs.existsSync(publicDir)) {
        fs.mkdirSync(publicDir, { recursive: true });
      }
      
      // Copy source CSS to public directory
      if (fs.existsSync(sourcePath)) {
        fs.copyFileSync(sourcePath, publicPath);
        
        // Get file sizes for verification
        const sourceStats = fs.statSync(sourcePath);
        const publicStats = fs.statSync(publicPath);
        
        results.push({
          theme: theme.name,
          status: 'success',
          sourceSize: sourceStats.size,
          publicSize: publicStats.size,
          sourcePath: theme.sourceCssFile,
          publicPath: `public${theme.cssFile}`,
        });
      } else {
        results.push({
          theme: theme.name,
          status: 'error',
          error: `Source CSS file not found: ${sourcePath}`,
        });
        allSuccess = false;
      }
    } catch (error) {
      results.push({
        theme: theme.name,
        status: 'error',
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      allSuccess = false;
    }
  }

  return { success: allSuccess, results };
}

export async function POST() {
  try {
    console.log('🎨 API: Starting theme CSS sync...');
    
    const syncResult = syncThemeCssFiles();
    
    if (syncResult.success) {
      console.log('✅ API: Theme CSS sync completed successfully');
      return NextResponse.json({
        success: true,
        message: 'Theme CSS files synced successfully',
        results: syncResult.results,
      });
    } else {
      console.warn('⚠️ API: Theme CSS sync completed with errors');
      return NextResponse.json({
        success: false,
        message: 'Theme CSS sync completed with some errors',
        results: syncResult.results,
      }, { status: 207 }); // 207 Multi-Status
    }
  } catch (error) {
    console.error('❌ API: Theme CSS sync failed:', error);
    return NextResponse.json({
      success: false,
      message: 'Failed to sync theme CSS files',
      error: error instanceof Error ? error.message : 'Unknown error',
    }, { status: 500 });
  }
}

interface StatusResult {
  theme: string;
  status: 'synced' | 'out-of-sync' | 'needs-sync' | 'source-missing' | 'unknown';
  sourceExists: boolean;
  publicExists: boolean;
  sourceSize: number;
  publicSize: number;
  inSync: boolean;
  sourcePath: string;
  publicPath: string;
}

export async function GET() {
  try {
    // Check sync status without syncing
    const results: StatusResult[] = [];
    
    for (const theme of THEMES) {
      const sourcePath = path.join(process.cwd(), theme.sourceCssFile);
      const publicPath = path.join(process.cwd(), 'public', theme.cssFile);
      
      const sourceExists = fs.existsSync(sourcePath);
      const publicExists = fs.existsSync(publicPath);
      
      let status: 'synced' | 'out-of-sync' | 'needs-sync' | 'source-missing' | 'unknown' = 'unknown';
      let sourceSize = 0;
      let publicSize = 0;
      let inSync = false;
      
      if (sourceExists && publicExists) {
        const sourceStats = fs.statSync(sourcePath);
        const publicStats = fs.statSync(publicPath);
        sourceSize = sourceStats.size;
        publicSize = publicStats.size;
        inSync = sourceStats.size === publicStats.size && sourceStats.mtime <= publicStats.mtime;
        status = inSync ? 'synced' : 'out-of-sync';
      } else if (sourceExists && !publicExists) {
        status = 'needs-sync';
        const sourceStats = fs.statSync(sourcePath);
        sourceSize = sourceStats.size;
      } else if (!sourceExists) {
        status = 'source-missing';
      }
      
      results.push({
        theme: theme.name,
        status,
        sourceExists,
        publicExists,
        sourceSize,
        publicSize,
        inSync,
        sourcePath: theme.sourceCssFile,
        publicPath: `public${theme.cssFile}`,
      });
    }
    
    return NextResponse.json({
      success: true,
      results,
    });
  } catch (error) {
    console.error('❌ API: Failed to check theme sync status:', error);
    return NextResponse.json({
      success: false,
      message: 'Failed to check theme sync status',
      error: error instanceof Error ? error.message : 'Unknown error',
    }, { status: 500 });
  }
}

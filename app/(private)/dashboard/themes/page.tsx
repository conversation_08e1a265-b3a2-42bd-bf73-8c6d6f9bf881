"use client";

import { useAuth } from "@/contexts/AuthenticationContext";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { getAllThemes } from "@/themes/theme-registry";
import { getUserPortfolios, updatePortfolioTheme } from "@/lib/portfolio-api";
import { AnyPortfolioData } from "@/lib/types";
import { toast } from "sonner";
import Image from "next/image";
import { Button } from "@/components/ui/button";
import { Loader2, Check, Palette, ArrowLeft } from "lucide-react";
import { FullScreenLoader } from "@/components/custom-ui/FullScreenLoader";
import Link from "next/link";

export default function ThemesPage() {
  const { user, loading: authLoading } = useAuth();
  const router = useRouter();
  const queryClient = useQueryClient();
  const [isSwitching, setIsSwitching] = useState<string | null>(null);

  const { data: portfolios, isLoading: portfoliosLoading } = useQuery<AnyPortfolioData[]>({
    queryKey: ["portfolios", user?.uid],
    queryFn: () => getUserPortfolios(user!.uid),
    enabled: !!user,
  });

  const portfolio = portfolios?.[0] || null;

  const switchThemeMutation = useMutation({
    mutationFn: async ({ portfolioId, templateId }: { portfolioId: string; templateId: string }) => {
      return updatePortfolioTheme(portfolioId, templateId);
    },
    onMutate: (variables: { portfolioId: string; templateId: string; }) => {
      setIsSwitching(variables.templateId);
    },
    onSuccess: async () => {
      await queryClient.invalidateQueries({ queryKey: ['portfolios', user?.uid] });
      await queryClient.refetchQueries({ queryKey: ['portfolios', user?.uid] });
      toast.success("Theme applied! Redirecting to the editor to customize.");
      router.push('/portfolio');
    },
    onError: (error: Error) => {
      toast.error(`Failed to switch theme: ${error.message}`);
    },
    onSettled: () => {
      setIsSwitching(null);
    },
  });

  const handleSelectTheme = (templateId: string) => {
    if (!portfolio) {
      toast.error("Could not find your portfolio to update.");
      return;
    }
    switchThemeMutation.mutate({ portfolioId: portfolio.id, templateId });
  };

  if (authLoading || portfoliosLoading) {
    return <FullScreenLoader text="Loading themes..." />;
  }

  if (!portfolio) {
    return (
      <div className="min-h-screen flex flex-col items-center justify-center text-center p-4">
        <h2 className="text-2xl font-bold mb-4">No Portfolio Found</h2>
        <p className="text-muted-foreground mb-6">You need to create a portfolio before you can change its theme.</p>
        <Button asChild>
          <Link href="/dashboard">Go to Dashboard</Link>
        </Button>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto px-6 py-8">
      <div className="flex items-center gap-4 mb-8">
        <Button variant="outline" size="icon" onClick={() => router.back()}>
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <div>
          <h1 className="text-3xl font-bold">Change Your Theme</h1>
          <p className="text-muted-foreground">Your content will be preserved. Select a new look for your portfolio.</p>
        </div>
      </div>

      <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
        {getAllThemes().map((template) => {
          const isCurrentTheme = portfolio.templateId === template.id;
          const isLoadingThisTemplate = isSwitching === template.id;

          return (
            <div
              key={template.id}
              className={`group relative bg-white rounded-2xl border hover:shadow-xl transition-all duration-500 overflow-hidden ${isCurrentTheme ? 'border-brandPrimary ring-4 ring-brandPrimary/20' : 'border-gray-200 hover:border-brandPrimary/30'}`}>
              <div className="relative aspect-[4/3] overflow-hidden bg-gradient-to-br from-gray-50 to-gray-100">
                <Image
                  src={template.preview || '/thumbnails/default-theme.jpg'}
                  alt={`${template.name} template preview`}
                  fill
                  className="object-cover transition-transform duration-700 group-hover:scale-105"
                />
                {isCurrentTheme && (
                  <div className="absolute inset-0 bg-black/50 flex items-center justify-center">
                    <div className="bg-white/90 backdrop-blur-sm rounded-full px-4 py-2 flex items-center gap-2">
                      <Check className="w-5 h-5 text-green-600" />
                      <span className="font-medium text-gray-800">Current Theme</span>
                    </div>
                  </div>
                )}
              </div>

              <div className="p-6">
                <h3 className="text-xl font-bold text-gray-900 mb-2 group-hover:text-brandPrimary transition-colors">
                  {template.name}
                </h3>
                <p className="text-gray-600 text-sm leading-relaxed mb-6">
                  {template.description}
                </p>
                <Button
                  onClick={() => handleSelectTheme(template.id)}
                  disabled={isCurrentTheme || isLoadingThisTemplate}
                  className="w-full bg-gradient-to-r from-brandPrimary to-brandSecondary hover:from-brandPrimary/90 hover:to-brandSecondary/90 text-white font-semibold py-3 rounded-xl transition-all duration-300 transform hover:scale-[1.02] active:scale-[0.98] shadow-lg hover:shadow-xl">
                  {isLoadingThisTemplate ? (
                    <><Loader2 className="mr-2 h-4 w-4 animate-spin" /> Applying Theme...</>
                  ) : isCurrentTheme ? (
                    <><Check className="mr-2 h-4 w-4" /> Applied</>
                  ) : (
                    <><Palette className="mr-2 h-4 w-4" /> Apply Theme</>
                  )}
                </Button>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
}

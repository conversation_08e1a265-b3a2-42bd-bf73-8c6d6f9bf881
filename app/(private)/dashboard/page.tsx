"use client";

import { useAuth } from "@/contexts/AuthenticationContext";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import React from "react";
import { AnyPortfolioData } from "@/lib/types";
import { getUserPortfolios, deletePortfolio } from '@/lib/portfolio-api';
import { useUniversalExport } from "@/hooks/useUniversalExport";
import { ConfirmationDialog } from '@/components/custom-ui/ConfirmationDialog';
import { useLogout } from "@/hooks/use-logout";
import { toast } from "sonner";
import { useState } from "react";

import { FullScreenLoader } from "@/components/custom-ui/FullScreenLoader";
import DashboardHeader from "@/components/dashboard-page/DashboardHeader";
import WelcomeHeader from "@/components/dashboard-page/WelcomeHeader";
import CurrentPortfolioCard from "@/components/dashboard-page/CurrentPortfolioCard";
import CategoryThemeGrid from "@/components/dashboard-page/CategoryThemeGrid";

export default function DashboardPage() {
  const { user, loading } = useAuth();

  const queryClient = useQueryClient();
  const { exportPortfolio, isExporting } = useUniversalExport();
  const logout = useLogout();
  const [isNavigating] = useState(false);
  const [isSigningOut, setIsSigningOut] = useState(false);

  const { data: portfolios, isLoading } = useQuery<AnyPortfolioData[]>({
    queryKey: ["portfolios", user?.uid],
    queryFn: () => getUserPortfolios(user!.uid),
    enabled: !!user,
  });

  // Get the first portfolio (for now, we'll show the first one)
  const portfolio = portfolios?.[0] || null;

  const handleSignOut = async () => {
    setIsSigningOut(true);
    await logout();
    setIsSigningOut(false);
  };



  const deleteMutation = useMutation({
    mutationFn: () => {
      if (!portfolio?.id) throw new Error('Portfolio ID not found');
      return deletePortfolio(portfolio.id);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['portfolios', user?.uid] });
      toast.success('Portfolio deleted successfully.');
    },
    onError: (error) => {
      toast.error(`Failed to delete portfolio: ${error.message}`);
    },
  });

  const [dialog, setDialog] = useState({ isOpen: false, onConfirm: () => {} });

  const handleDeletePortfolio = () => {
    setDialog({ isOpen: true, onConfirm: () => deleteMutation.mutate() });
  };

  const handleExport = async () => {
    if (!portfolio) {
      toast.error("No portfolio data available for export.");
      return;
    }

    try {
      await exportPortfolio(portfolio);
      toast.success("Export successful! Check your downloads.");
    } catch (error: unknown) {
      if (error instanceof Error) {
        toast.error(`Export failed: ${error.message}`);
      } else {
        toast.error("Export failed: An unknown error occurred.");
      }
    }
  };

  if (loading || isLoading || isNavigating || isSigningOut) {
    return (
      <FullScreenLoader
        text={
          isNavigating
            ? "Setting up your portfolio editor..."
            : isSigningOut
            ? "Signing out..."
            : "Loading your dashboard..."
        }
      />
    );
  }

  return (
    <div className="min-h-screen">
      {/* Navigation Header */}
      <DashboardHeader
        user={user}
        onSignOut={handleSignOut}
        isSigningOut={isSigningOut}
      />

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 py-6 sm:py-8">
        <div className="space-y-6 sm:space-y-8">
          {/* Welcome Header */}
          <WelcomeHeader user={user} hasPortfolio={!!portfolio} />

          {/* Current Portfolio Card - Only show if user has a portfolio */}
          {portfolio && (
            <CurrentPortfolioCard
              portfolio={portfolio}
              onExport={handleExport}
              isExporting={isExporting}
              onDelete={handleDeletePortfolio}
            />
          )}

          {/* Category Theme Grid - Always show */}
          <CategoryThemeGrid
            user={user}
            portfolio={portfolio}
          />
        </div>
      </main>
      <ConfirmationDialog
        isOpen={dialog.isOpen}
        onClose={() => setDialog({ isOpen: false, onConfirm: () => {} })}
        onConfirm={() => {
          dialog.onConfirm();
          setDialog({ isOpen: false, onConfirm: () => {} });
        }}
        title="Are you absolutely sure?"
        description="This action is irreversible and will permanently delete all of your portfolio data."
        isDestructive
        isPending={deleteMutation.isPending}
      />
    </div>
  );
}

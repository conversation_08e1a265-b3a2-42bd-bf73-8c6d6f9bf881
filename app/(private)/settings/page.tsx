'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog';
import { getUser, deleteUserAccount } from '@/lib/user-api';
import { User } from '@/lib/types';
import { toast } from 'sonner';
import { Crown, Trash2, User as UserIcon, Loader2, AlertTriangle, ArrowLeft } from 'lucide-react';
import { useAuth } from '@/contexts/AuthenticationContext';
import { GoogleAuthProvider, reauthenticateWithPopup } from 'firebase/auth';
import { FullScreenLoader } from '@/components/custom-ui/FullScreenLoader';
import DashboardHeader from '@/components/dashboard-page/DashboardHeader';
import { useLogout } from '@/hooks/use-logout';

export default function SettingsPage() {
  const { firebaseUser } = useAuth();
  const router = useRouter();
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [deleting, setDeleting] = useState(false);
  const [reauthenticating, setReauthenticating] = useState(false);
  const logout = useLogout();
  const [isSigningOut, setIsSigningOut] = useState(false);

  // Load user data
  useEffect(() => {
    const loadUser = async () => {
      if (!firebaseUser) return;

      try {
        const userData = await getUser(firebaseUser.uid);
        if (userData) {
          setUser(userData);
        }
      } catch (error) {
        console.error('Error loading user:', error);
        toast.error('Failed to load user settings');
      } finally {
        setLoading(false);
      }
    };

    loadUser();
  }, [firebaseUser]);

  // Helper function to delete cookies
  const deleteCookie = (name: string) => {
    document.cookie = name + '=; Path=/; Expires=Thu, 01 Jan 1970 00:00:01 GMT;';
  };

  // Re-authenticate user before sensitive operations
  const reauthenticateUser = async (): Promise<boolean> => {
    if (!firebaseUser) return false;

    setReauthenticating(true);
    try {
      const provider = new GoogleAuthProvider();
      await reauthenticateWithPopup(firebaseUser, provider);
      toast.success('Authentication confirmed');
      return true;
    } catch (error) {
      console.error('Re-authentication failed:', error);
      toast.error('Authentication failed. Please try again.');
      return false;
    } finally {
      setReauthenticating(false);
    }
  };

  // Delete account
  const handleDeleteAccount = async () => {
    if (!user || !firebaseUser) return;

    // First, re-authenticate the user
    const isReauthenticated = await reauthenticateUser();
    if (!isReauthenticated) {
      return;
    }

    setDeleting(true);

    try {
      // Clear the auth cookie first to prevent auth state listener from interfering
      deleteCookie('firebaseIdToken');

      // Delete the account (this will trigger auth state change but cookie is already cleared)
      await deleteUserAccount(user.uid, firebaseUser);

      // Show success message
      toast.success('Account deleted successfully. You can sign in again anytime.');

      // Wait a bit for user to see the message, then redirect
      setTimeout(() => {
        router.replace('/login');
      }, 2000);

    } catch (error) {
      console.error('Error deleting account:', error);
      
      // Handle specific Firebase errors
      if (error instanceof Error && error.message.includes('auth/requires-recent-login')) {
        toast.error('Please re-authenticate and try again.');
      } else {
        toast.error('Failed to delete account. Please try again.');
      }
      
      setDeleting(false); // Always re-enable UI on error
    }
  };

  const handleSignOut = async () => {
    setIsSigningOut(true);
    await logout();
    setIsSigningOut(false);
  };

  if (loading) {
    return <FullScreenLoader text="Loading settings..." />;
  }

  if (!user) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center bg-white rounded-2xl p-8 shadow-sm border border-gray-200">
          <UserIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h1 className="text-xl font-semibold text-gray-900 mb-2">User not found</h1>
          <p className="text-gray-600 mb-6">Unable to load your account settings.</p>
          <Button onClick={() => router.push('/dashboard')} className="bg-brandAccent hover:bg-brandAccent/90">
            Go to Dashboard
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Loading Overlays */}
      {deleting && (
        <div className="fixed inset-0 bg-black/60 backdrop-blur-sm z-50 flex items-center justify-center">
          <div className="bg-white rounded-2xl p-8 shadow-2xl text-center max-w-sm mx-4">
            <div className="w-12 h-12 mx-auto mb-4 relative">
              <Loader2 className="w-12 h-12 animate-spin text-red-500" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Deleting Account</h3>
            <p className="text-gray-600 text-sm">Please wait while we delete your account and all data...</p>
          </div>
        </div>
      )}

      {reauthenticating && (
        <div className="fixed inset-0 bg-black/60 backdrop-blur-sm z-50 flex items-center justify-center">
          <div className="bg-white rounded-2xl p-8 shadow-2xl text-center max-w-sm mx-4">
            <div className="w-12 h-12 mx-auto mb-4 relative">
              <Loader2 className="w-12 h-12 animate-spin text-brandAccent" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Confirming Identity</h3>
            <p className="text-gray-600 text-sm">Please complete the authentication process...</p>
          </div>
        </div>
      )}

      {/* Dashboard Header */}
      <DashboardHeader
        user={user}
        onSignOut={handleSignOut}
        isSigningOut={isSigningOut}
        title="Account Settings"
        subtitle="Manage your subscription and account preferences"
      />

      {/* Main Content */}
      <div className="max-w-4xl mx-auto px-4 py-8">
        {/* Navigation */}
        <div className="mb-8">
          <Button
            variant="ghost"
            onClick={() => router.push('/dashboard')}
            className="flex items-center gap-2 text-textSecondary hover:text-brandPrimary"
          >
            <ArrowLeft className="w-4 h-4" />
            Back to Dashboard
          </Button>
        </div>

        {/* Settings Grid */}
        <div className="grid lg:grid-cols-2 gap-8">
          {/* Account Information */}
          <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-200">
            <div className="flex items-center gap-3 mb-6">
              <div className="w-10 h-10 bg-blue-50 rounded-full flex items-center justify-center">
                <UserIcon className="w-5 h-5 text-blue-600" />
              </div>
              <div>
                <h2 className="text-lg font-semibold text-gray-900">Account Information</h2>
                <p className="text-sm text-gray-600">Your profile details</p>
              </div>
            </div>

            <div className="space-y-4">
              <div className="flex items-center justify-between py-3 border-b border-gray-100">
                <span className="text-sm font-medium text-gray-700">Display Name</span>
                <span className="text-sm text-gray-900">{user.displayName || 'Not set'}</span>
              </div>
              <div className="flex items-center justify-between py-3 border-b border-gray-100">
                <span className="text-sm font-medium text-gray-700">Email</span>
                <span className="text-sm text-gray-900">{user.email}</span>
              </div>
              <div className="flex items-center justify-between py-3">
                <span className="text-sm font-medium text-gray-700">Member Since</span>
                <span className="text-sm text-gray-900">
                  {new Date(user.createdAt).toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                  })}
                </span>
              </div>
            </div>
          </div>

          {/* Subscription Plan */}
          <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-200">
            <div className="flex items-center gap-3 mb-6">
              <div className="w-10 h-10 bg-yellow-50 rounded-full flex items-center justify-center">
                <Crown className="w-5 h-5 text-yellow-600" />
              </div>
              <div>
                <h2 className="text-lg font-semibold text-gray-900">Subscription Plan</h2>
                <p className="text-sm text-gray-600">Your current plan and features</p>
              </div>
            </div>

            <div className="space-y-6">
              {/* Current Plan */}
              <div className="p-4 bg-gray-50 rounded-xl">
                <div className="flex items-center justify-between mb-3">
                  <span className="text-sm font-medium text-gray-700">Current Plan</span>
                  <span className="px-3 py-1 bg-gray-200 text-gray-800 text-xs font-medium rounded-full">
                    FREE
                  </span>
                </div>
                <p className="text-sm text-gray-600">
                  Access to 2 free themes and basic portfolio features
                </p>
              </div>

              {/* Premium Plan */}
              <div className="p-4 border border-orange-200 bg-orange-50 rounded-xl">
                <div className="flex items-center justify-between mb-3">
                  <span className="text-sm font-medium text-gray-700">Premium Plan</span>
                  <span className="px-3 py-1 bg-orange-200 text-orange-800 text-xs font-medium rounded-full">
                    COMING SOON
                  </span>
                </div>
                <p className="text-sm text-gray-600">
                  Premium themes, custom domains, and advanced features
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Danger Zone */}
        <div className="mt-12">
          <div className="bg-white rounded-2xl p-6 shadow-sm border border-red-200">
            <div className="flex items-center gap-3 mb-6">
              <div className="w-10 h-10 bg-red-50 rounded-full flex items-center justify-center">
                <AlertTriangle className="w-5 h-5 text-red-600" />
              </div>
              <div>
                <h2 className="text-lg font-semibold text-red-600">Danger Zone</h2>
                <p className="text-sm text-gray-600">Irreversible actions that will permanently affect your account</p>
              </div>
            </div>

            <div className="p-4 bg-red-50 rounded-xl border border-red-200">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <h3 className="text-sm font-medium text-red-900 mb-1">Delete Account</h3>
                  <p className="text-sm text-red-700 mb-4">
                    Permanently delete your account and all associated data. This action cannot be undone.
                  </p>

                  <AlertDialog>
                    <AlertDialogTrigger asChild>
                      <Button
                        variant="destructive"
                        size="sm"
                        className="bg-red-600 hover:bg-red-700 text-white"
                      >
                        <Trash2 className="w-4 h-4 mr-2" />
                        Delete Account
                      </Button>
                    </AlertDialogTrigger>
                    <AlertDialogContent className="max-w-md">
                      <AlertDialogHeader>
                        <AlertDialogTitle className="flex items-center gap-2 text-red-600">
                          <AlertTriangle className="w-5 h-5" />
                          Delete Account & All Data?
                        </AlertDialogTitle>
                        <AlertDialogDescription asChild>
                          <div className="space-y-4 text-sm">
                            <div className="p-3 bg-red-50 rounded-lg border border-red-200">
                              <p className="font-medium text-red-900 mb-2">This action cannot be undone.</p>
                              <p className="text-red-800">We will permanently delete:</p>
                            </div>

                            <ul className="space-y-2 text-gray-700">
                              <li className="flex items-center gap-2">
                                <div className="w-1.5 h-1.5 bg-red-500 rounded-full"></div>
                                Your user account and profile
                              </li>
                              <li className="flex items-center gap-2">
                                <div className="w-1.5 h-1.5 bg-red-500 rounded-full"></div>
                                All your portfolio data and content
                              </li>
                              <li className="flex items-center gap-2">
                                <div className="w-1.5 h-1.5 bg-red-500 rounded-full"></div>
                                All associated files and images
                              </li>
                              <li className="flex items-center gap-2">
                                <div className="w-1.5 h-1.5 bg-red-500 rounded-full"></div>
                                Your account settings and preferences
                              </li>
                            </ul>

                            <div className="p-3 bg-orange-50 rounded-lg border border-orange-200">
                              <p className="text-orange-800 text-xs">
                                <strong>Security Notice:</strong> You will need to re-authenticate with Google to confirm your identity before account deletion.
                              </p>
                            </div>
                          </div>
                        </AlertDialogDescription>
                      </AlertDialogHeader>
                      <AlertDialogFooter>
                        <AlertDialogCancel className="text-gray-600">Cancel</AlertDialogCancel>
                        <AlertDialogAction
                          onClick={handleDeleteAccount}
                          disabled={deleting || reauthenticating}
                          className="bg-red-600 hover:bg-red-700"
                        >
                          {deleting ? (
                            <div className="flex items-center gap-2">
                              <Loader2 className="w-4 h-4 animate-spin" />
                              <span>Deleting...</span>
                            </div>
                          ) : reauthenticating ? (
                            <div className="flex items-center gap-2">
                              <Loader2 className="w-4 h-4 animate-spin" />
                              <span>Authenticating...</span>
                            </div>
                          ) : (
                            'Yes, Delete Everything'
                          )}
                        </AlertDialogAction>
                      </AlertDialogFooter>
                    </AlertDialogContent>
                  </AlertDialog>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
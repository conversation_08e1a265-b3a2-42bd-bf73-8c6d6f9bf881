"use client";
import React, {
  createContext,
  useState,
  useEffect,
  useContext,
  ReactNode,
} from "react";
import { onAuthStateChanged, onIdTokenChanged, User as FirebaseUser } from "firebase/auth";
import { auth } from "@/lib/firebase";
import { User } from "@/lib/types";
import { FullScreenLoader } from "@/components/custom-ui/FullScreenLoader";
import { createOrUpdateUser, getUser } from "@/lib/user-api";

interface AuthContextType {
  user: User | null;
  firebaseUser: FirebaseUser | null;
  loading: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider = ({ children }: { children: ReactNode }) => {
  const [user, setUser] = useState<User | null>(null);
  const [firebaseUser, setFirebaseUser] = useState<FirebaseUser | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Listen for ID token changes and update cookie
    const unsubscribeToken = onIdTokenChanged(auth, async (user) => {
      if (user) {
        const token = await user.getIdToken();
        document.cookie = `firebaseIdToken=${token}; path=/`;
      } else {
        document.cookie = "firebaseIdToken=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT";
      }
    });
    const unsubscribe = onAuthStateChanged(auth, async (user) => {
      setFirebaseUser(user);
      if (user) {
        const token = await user.getIdToken();
        document.cookie = `firebaseIdToken=${token}; path=/`;
        const appUser = await getUser(user.uid);
        if (appUser) {
          setUser(appUser);
        } else {
          // If the user doesn't exist in the database, create them
          const newUser = await createOrUpdateUser({
            uid: user.uid,
            email: user.email!,
            displayName: user.displayName ?? undefined,
            photoURL: user.photoURL ?? undefined,
          });
          setUser(newUser);
        }
      } else {
        document.cookie =
          "firebaseIdToken=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT";
        setUser(null);
      }
      setLoading(false);
    });
    return () => {
      unsubscribe();
      unsubscribeToken();
    };
  }, []);

  if (loading) {
    return <FullScreenLoader />;
  }

  return (
    <AuthContext.Provider value={{ user, firebaseUser, loading }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};

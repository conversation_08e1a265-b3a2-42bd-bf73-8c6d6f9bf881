"use client";
import { createContext, useContext } from 'react';

// Create a context that will hold a simple boolean value.
const ExportContext = createContext<boolean>(false);

// Export the provider component. We'll use this in our API route and our live editor.
export const ExportProvider = ExportContext.Provider;

// Export a custom hook that components can use to easily access the value.
export function useIsExport(): boolean {
    const context = useContext(ExportContext);

    // Also check for global export flag and document attributes
    const globalFlag = typeof window !== 'undefined' &&
        (window as typeof window & { __PORTFOLIO_EXPORT__?: boolean }).__PORTFOLIO_EXPORT__;
    const documentFlag = typeof document !== 'undefined' &&
        document.documentElement.hasAttribute('data-export');

    return context || globalFlag || documentFlag;
}
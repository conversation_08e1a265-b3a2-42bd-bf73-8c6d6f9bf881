"use client";
import React, { createContext, useContext, useState, useEffect } from 'react';
import { PortfolioData } from '@/lib/types';
import { ValidationResult, validatePortfolio } from '@/lib/portfolio-validation';

interface ValidationContextType {
  validationResult: ValidationResult;
  isValidating: boolean;
  validateData: (data: PortfolioData) => ValidationResult;
  clearValidation: () => void;
}

const ValidationContext = createContext<ValidationContextType | undefined>(undefined);

interface ValidationProviderProps {
  children: React.ReactNode;
}

export function ValidationProvider({ children }: ValidationProviderProps) {
  const [validationResult, setValidationResult] = useState<ValidationResult>({
    isValid: true,
    errors: [],
    warnings: []
  });
  const [isValidating, setIsValidating] = useState(false);

  const validateData = (data: PortfolioData): ValidationResult => {
    setIsValidating(true);
    
    try {
      const result = validatePortfolio(data);
      setValidationResult(result);
      return result;
    } finally {
      setIsValidating(false);
    }
  };

  const clearValidation = () => {
    setValidationResult({
      isValid: true,
      errors: [],
      warnings: []
    });
  };

  return (
    <ValidationContext.Provider value={{
      validationResult,
      isValidating,
      validateData,
      clearValidation
    }}>
      {children}
    </ValidationContext.Provider>
  );
}

export function useValidation() {
  const context = useContext(ValidationContext);
  if (context === undefined) {
    throw new Error('useValidation must be used within a ValidationProvider');
  }
  return context;
}

// Safe hook that can be called even when not in ValidationProvider
export function useValidationSafe() {
  const context = useContext(ValidationContext);
  return context; // Returns undefined if not in ValidationProvider
}

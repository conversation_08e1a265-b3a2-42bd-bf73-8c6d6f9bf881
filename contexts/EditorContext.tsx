"use client";
import { createContext, useContext, useReducer, Dispatch } from 'react';
import { AnyPortfolioData, Project, Experience, isGeneralPortfolio, isITPortfolio } from '@/lib/types';
import { ValidationResult } from '@/lib/portfolio-validation';

// State and Actions
type EditorState = {
    formData: AnyPortfolioData;
    isUploading: { type: string; id?: string } | null;
    saveStatus: 'idle' | 'saving' | 'saved' | 'error';
    validationResult?: ValidationResult;
};

export type Action =
    | { type: 'INITIALIZE_DATA'; payload: AnyPortfolioData }
    | { type: 'UPDATE_FIELD'; payload: { field: string; value: unknown } }
    | { type: 'ADD_PROJECT' }
    | { type: 'UPDATE_PROJECT'; payload: { index: number; field: keyof Project; value: unknown } }
    | { type: 'DELETE_PROJECT'; payload: { id: string } }
    | { type: 'ADD_EXPERIENCE' }
    | { type: 'UPDATE_EXPERIENCE'; payload: { index: number; field: keyof Experience; value: unknown } }
    | { type: 'DELETE_EXPERIENCE'; payload: { id: string } }
    | { type: 'SET_UPLOADING'; payload: { type: string; id?: string } | null }
    | { type: 'SET_SAVE_STATUS'; payload: EditorState['saveStatus'] }
    | { type: 'SET_VALIDATION_RESULT'; payload: ValidationResult }
    | { type: 'SYNC_SAVED_DATA'; payload: AnyPortfolioData };

// Reducer Function
function editorReducer(state: EditorState, action: Action): EditorState {
    switch (action.type) {
        case 'INITIALIZE_DATA':
            return { ...state, formData: action.payload };
        // It simply replaces the form data with the new, authoritative state from the server.
        case 'SYNC_SAVED_DATA':
            return { ...state, formData: action.payload };

        case 'UPDATE_FIELD':
            return { ...state, formData: { ...state.formData, [action.payload.field]: action.payload.value } };
        case 'ADD_PROJECT': {
            // Only add projects for portfolio types that support them
            if (isGeneralPortfolio(state.formData) || isITPortfolio(state.formData)) {
                const timestamp = Date.now();
                const randomSuffix = Math.random().toString(36).substring(2, 11);
                const newProject: Project = {
                    id: `project-${timestamp}-${randomSuffix}`,
                    title: '',
                    description: '',
                    url: '',
                    liveUrl: '',
                    imageUrl: ''
                };
                return { ...state, formData: { ...state.formData, projects: [...state.formData.projects, newProject] } as AnyPortfolioData };
            }
            return state;
        }
        case 'UPDATE_PROJECT': {
            if (isGeneralPortfolio(state.formData) || isITPortfolio(state.formData)) {
                const updatedProjects = [...state.formData.projects];
                updatedProjects[action.payload.index] = { ...updatedProjects[action.payload.index], [action.payload.field]: action.payload.value };
                return { ...state, formData: { ...state.formData, projects: updatedProjects } as AnyPortfolioData };
            }
            return state;
        }
        case 'DELETE_PROJECT': {
            if (isGeneralPortfolio(state.formData) || isITPortfolio(state.formData)) {
                return { ...state, formData: { ...state.formData, projects: state.formData.projects.filter((p: Project) => p.id !== action.payload.id) } as AnyPortfolioData };
            }
            return state;
        }
        case 'ADD_EXPERIENCE': {
            const timestamp = Date.now();
            const randomSuffix = Math.random().toString(36).substring(2, 11);
            const newExperience: Experience = {
                id: `exp-${timestamp}-${randomSuffix}`,
                role: '',
                company: '',
                duration: '',
                description: '',
                location: '',
                companyUrl: '',
            };

            return {
                ...state,
                formData: {
                    ...state.formData,
                    experiences: [...state.formData.experiences, newExperience],
                },
            };
        }
        case 'UPDATE_EXPERIENCE': {
            const updatedExperiences = [...state.formData.experiences];
            updatedExperiences[action.payload.index] = {
                ...updatedExperiences[action.payload.index],
                [action.payload.field]: action.payload.value,
            };
            return {
                ...state,
                formData: {
                    ...state.formData,
                    experiences: updatedExperiences,
                },
            };
        }
        case 'DELETE_EXPERIENCE': {
            return {
                ...state,
                formData: {
                    ...state.formData,
                    experiences: state.formData.experiences.filter(
                        (exp) => exp.id !== action.payload.id
                    ),
                },
            };
        }
        case 'SET_UPLOADING':
            return { ...state, isUploading: action.payload };
        case 'SET_SAVE_STATUS':
            return { ...state, saveStatus: action.payload };
        case 'SET_VALIDATION_RESULT':
            return { ...state, validationResult: action.payload };
        default:
            throw new Error(`Unhandled action type`);
    }
}

// Context
interface EditorContextType {
    state: EditorState;
    dispatch: Dispatch<Action>;
}
const EditorContext = createContext<EditorContextType | undefined>(undefined);

// Provider
export function EditorProvider({ children, initialData }: { children: React.ReactNode, initialData: AnyPortfolioData }) {
    const [state, dispatch] = useReducer(editorReducer, {
        formData: initialData,
        isUploading: null,
        saveStatus: 'idle',
        validationResult: undefined,
    });
    return <EditorContext.Provider value={{ state, dispatch }}>{children}</EditorContext.Provider>;
}

// Hook
export function useEditor() {
    const context = useContext(EditorContext);
    if (context === undefined) {
        throw new Error('useEditor must be used within an EditorProvider');
    }
    return context;
}

// Safe hook that can be called even when not in editing mode
export function useEditorSafe() {
    const context = useContext(EditorContext);
    return context; // Returns undefined if not in EditorProvider
}
"use client";

import { useState, useEffect } from 'react';

// This custom hook takes a value and a delay time.
// It will only return the latest value after the user has stopped providing a new value for the specified delay.
export function useDebounce<T>(value: T, delay: number): T {
  // State to store the debounced value
  const [debouncedValue, setDebouncedValue] = useState<T>(value);

  useEffect(() => {
    // Set up a timer that will update the debounced value after the delay
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    // This is the cleanup function.
    // If the value changes again before the timer is up, we clear the previous timer and start a new one.
    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]); // Only re-run the effect if value or delay changes

  return debouncedValue;
}
import { useEffect } from 'react';

/**
 * Development hook to ensure theme CSS files are synced
 * This runs only in development mode and checks if CSS files need syncing
 */
export function useThemeSync() {
  useEffect(() => {
    // Only run in development mode
    if (process.env.NODE_ENV !== 'development') {
      return;
    }

    // Check if theme CSS files are accessible
    const checkThemeSync = async () => {
      const themes = [
        { id: 'modern-theme-v1', cssPath: '/themes/modern/modern-compiled.css' },
        { id: 'creative-theme-v1', cssPath: '/themes/creative-minimalist/creative-minimalist-compiled.css' },
      ];

      for (const theme of themes) {
        try {
          const response = await fetch(theme.cssPath);
          if (!response.ok) {
            console.warn(`🎨 Theme CSS not found: ${theme.cssPath}`);
            console.warn('💡 Run "npm run sync-themes" to sync CSS files');
          }
        } catch (error) {
          console.warn(`🎨 Failed to check theme CSS: ${theme.cssPath}`, error);
        }
      }
    };

    // Check theme sync status on mount
    checkThemeSync();
  }, []);
}

/**
 * Hook to ensure theme CSS is available before export
 */
export function useExportThemeSync() {
  const ensureThemeSync = async (themeId: string): Promise<boolean> => {
    try {
      // Import theme registry to get CSS URL
      const { getThemeCssUrl } = await import('@/themes/theme-registry');
      const cssUrl = getThemeCssUrl(themeId);
      
      if (!cssUrl) {
        console.error(`❌ No CSS URL found for theme: ${themeId}`);
        return false;
      }

      // Check if CSS is accessible
      const response = await fetch(cssUrl);
      if (!response.ok) {
        console.error(`❌ Theme CSS not accessible: ${cssUrl}`);
        console.error('💡 Run "npm run sync-themes" to sync CSS files');
        return false;
      }

      const cssContent = await response.text();
      if (!cssContent || cssContent.trim().length === 0) {
        console.error(`❌ Theme CSS is empty: ${cssUrl}`);
        return false;
      }

      console.log(`✅ Theme CSS verified: ${themeId} (${cssContent.length} characters)`);
      return true;
    } catch (error) {
      console.error(`❌ Failed to verify theme CSS for ${themeId}:`, error);
      return false;
    }
  };

  return { ensureThemeSync };
}

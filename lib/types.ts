// User Management Types
export type UserPlan = 'free' | 'premium';

export interface User {
  uid: string;
  email: string;
  displayName: string | null;
  photoURL: string | null;
  plan: UserPlan;
  createdAt: Date;
  updatedAt: Date;
  // Premium features
  premiumExpiresAt?: Date;
  // Settings
  settings: {
    emailNotifications: boolean;
    publicProfile: boolean;
  };
}

// Theme Categories and Profession Types
export type ThemeCategory = 'general' | 'healthcare' | 'technology' | 'creative' | 'business' | 'education' | 'organization';
export type ProfessionType = 'general' | 'doctor' | 'nurse' | 'it-professional' | 'photographer' | 'designer' | 'teacher' | 'business-analyst' | 'traveller';
export type OrganizationType = 'general' | 'hospital' | 'tech-company' | 'design-agency' | 'educational-institution' | 'non-profit';

// Base Portfolio Data Types
export interface Project {
  id: string;
  title: string;
  description: string;
  url?: string;
  liveUrl?: string;
  imageUrl?: string;
}

export interface Experience {
  id: string;
  role: string;
  company: string;
  duration: string;
  description?: string;
  location?: string;
  companyUrl?: string;
}

export interface Skill {
  id: string;
  name: string;
  category?: 'web-development' | 'mobile-development' | 'design' | 'data-science' | 'devops' | 'marketing' | 'business' | 'other';
}

export interface SocialLinks {
  githubUrl?: string;
  linkedinUrl?: string;
  twitterUrl?: string;
}

// Profession-Specific Data Types
export interface MedicalExperience extends Experience {
  specialization?: string;
  certifications?: string[];
  hospitalType?: 'public' | 'private' | 'clinic' | 'research';
}

export interface ITProject extends Project {
  technologies?: string[];
  githubUrl?: string;
  deploymentUrl?: string;
  teamSize?: number;
  role?: string;
}

export interface CreativeProject extends Project {
  clientName?: string;
  category?: 'branding' | 'web-design' | 'photography' | 'illustration' | 'video';
  awards?: string[];
  collaborators?: string[];
}

export interface TravelExperience {
  id: string;
  destination: string;
  duration: string;
  description: string;
  highlights?: string[];
  imageUrls?: string[];
  travelType?: 'solo' | 'group' | 'business' | 'volunteer';
}




// Base Portfolio Data Interface
export interface BasePortfolioData {
  id: string; // Portfolio document ID
  userId: string; // Reference to user
  isPublished: boolean;
  slug: string;
  templateId: string;
  themeCategory: ThemeCategory;
  professionType: ProfessionType;
  userName: string;
  profession: string;
  about?: string;
  bio?: string;
  profileImageUrl?: string;
  resumeUrl?: string;
  contactEmail: string;
  email?: string;
  phone?: string;
  githubUrl?: string;
  linkedinUrl?: string;
  twitterUrl?: string;
  // Metadata
  createdAt: Date;
  updatedAt: Date;
}

// General Portfolio Data (backward compatible)
export interface PortfolioData extends BasePortfolioData {
  themeCategory: 'general';
  professionType: 'general';
  qualifications?: string;
  qualification1?: string;
  qualification2?: string;
  qualification3?: string;
  qualification4?: string;
  projects: Project[];
  experiences: Experience[];
  skills: Skill[];
}

// Medical Professional Portfolio
export interface MedicalPortfolioData extends BasePortfolioData {
  themeCategory: 'healthcare';
  professionType: 'doctor' | 'nurse';
  medicalLicense?: string;
  specializations: string[];
  certifications: string[];
  education: {
    degree: string;
    institution: string;
    year: string;
    location?: string;
  }[];
  experiences: MedicalExperience[];
  skills: Skill[];
  publications?: {
    id: string;
    title: string;
    journal: string;
    year: string;
    url?: string;
  }[];
  awards?: string[];
}

// IT Professional Portfolio
export interface ITPortfolioData extends BasePortfolioData {
  themeCategory: 'technology';
  professionType: 'it-professional';
  qualification1?: string;
  qualification2?: string;
  projects: ITProject[];
  experiences: Experience[];
  skills: Skill[];
  certifications?: string[];
  openSourceContributions?: {
    id: string;
    projectName: string;
    description: string;
    url: string;
    role: string;
  }[];
}

// Creative Professional Portfolio
export interface CreativePortfolioData extends BasePortfolioData {
  themeCategory: 'creative';
  professionType: 'photographer' | 'designer';
  artistStatement?: string;
  projects: CreativeProject[];
  experiences: Experience[];
  skills: Skill[];
  exhibitions?: {
    id: string;
    name: string;
    venue: string;
    date: string;
    type: 'solo' | 'group';
  }[];
  awards?: string[];
}

// Travel Professional Portfolio
export interface TravelPortfolioData extends BasePortfolioData {
  themeCategory: 'creative';
  professionType: 'traveller';
  travelPhilosophy?: string;
  destinations: TravelExperience[];
  experiences: Experience[];
  skills: Skill[];
  languages?: string[];
  travelStats?: {
    countriesVisited: number;
    continentsVisited: number;
    totalTravelDays: number;
  };
}

// Union type for all portfolio data types
export type AnyPortfolioData = PortfolioData | MedicalPortfolioData | ITPortfolioData | CreativePortfolioData | TravelPortfolioData;

// Default portfolio data factory functions
export const createDefaultPortfolioData = (
  portfolioId: string,
  userId: string,
  user: User,
  themeCategory: ThemeCategory = 'general',
  professionType: ProfessionType = 'general'
): AnyPortfolioData => {
  const baseData = {
    id: portfolioId,
    userId,
    isPublished: false,
    slug: `${user.displayName?.toLowerCase().replace(/\s+/g, '-')}`,
    templateId: 'creative-theme-v1',
    themeCategory,
    professionType,
    userName: user.displayName || '',
    profession: '',
    about: '',
    bio: '',
    contactEmail: user.email,
    email: user.email,
    profileImageUrl: '',
    resumeUrl: '',
    githubUrl: '',
    linkedinUrl: '',
    twitterUrl: '',
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  // Return profession-specific data structure
  switch (professionType) {
    case 'doctor':
    case 'nurse':
      return {
        ...baseData,
        themeCategory: 'healthcare' as const,
        professionType,
        medicalLicense: '',
        specializations: [],
        certifications: [],
        education: [],
        experiences: [{
          id: 'default-experience',
          role: '',
          company: '',
          duration: '',
          description: '',
          location: '',
          specialization: '',
          certifications: [],
          hospitalType: 'public' as const
        }],
        skills: [
          { id: 'default-skill-1', name: '', category: 'other' as const },
          { id: 'default-skill-2', name: '', category: 'other' as const },
          { id: 'default-skill-3', name: '', category: 'other' as const }
        ],
        publications: [],
        awards: []
      } as MedicalPortfolioData;

    case 'it-professional':
      return {
        ...baseData,
        themeCategory: 'technology' as const,
        professionType,
        qualification1: '',
        qualification2: '',
        projects: [{
          id: 'default-project',
          title: '',
          description: '',
          url: '',
          liveUrl: '',
          imageUrl: '',
          technologies: [],
          githubUrl: '',
          deploymentUrl: '',
          teamSize: 1,
          role: ''
        }],
        experiences: [{
          id: 'default-experience',
          role: '',
          company: '',
          duration: '',
          description: '',
          location: ''
        }],
        skills: [
          { id: 'default-skill-1', name: '', category: 'web-development' as const },
          { id: 'default-skill-2', name: '', category: 'web-development' as const },
          { id: 'default-skill-3', name: '', category: 'web-development' as const }
        ],
        certifications: [],
        openSourceContributions: []
      } as ITPortfolioData;

    case 'photographer':
    case 'designer':
      return {
        ...baseData,
        themeCategory: 'creative' as const,
        professionType,
        artistStatement: '',
        projects: [{
          id: 'default-project',
          title: '',
          description: '',
          url: '',
          liveUrl: '',
          imageUrl: '',
          clientName: '',
          category: 'branding' as const,
          awards: [],
          collaborators: []
        }],
        experiences: [{
          id: 'default-experience',
          role: '',
          company: '',
          duration: '',
          description: '',
          location: ''
        }],
        skills: [
          { id: 'default-skill-1', name: '', category: 'design' as const },
          { id: 'default-skill-2', name: '', category: 'design' as const },
          { id: 'default-skill-3', name: '', category: 'design' as const }
        ],
        exhibitions: [],
        awards: []
      } as CreativePortfolioData;

    case 'traveller':
      return {
        ...baseData,
        themeCategory: 'creative' as const,
        professionType,
        travelPhilosophy: '',
        destinations: [{
          id: 'default-destination',
          destination: '',
          duration: '',
          description: '',
          highlights: [],
          imageUrls: [],
          travelType: 'solo' as const
        }],
        experiences: [{
          id: 'default-experience',
          role: '',
          company: '',
          duration: '',
          description: '',
          location: ''
        }],
        skills: [
          { id: 'default-skill-1', name: '', category: 'other' as const },
          { id: 'default-skill-2', name: '', category: 'other' as const },
          { id: 'default-skill-3', name: '', category: 'other' as const }
        ],
        languages: [],
        travelStats: {
          countriesVisited: 0,
          continentsVisited: 0,
          totalTravelDays: 0
        }
      } as TravelPortfolioData;

    default:
      return {
        ...baseData,
        themeCategory: 'general' as const,
        professionType: 'general' as const,
        qualification1: '',
        qualification2: '',
        projects: [{
          id: 'default-project',
          title: '',
          description: '',
          url: '',
          liveUrl: '',
          imageUrl: ''
        }],
        experiences: [{
          id: 'default-experience',
          role: '',
          company: '',
          duration: '',
          description: '',
          location: ''
        }],
        skills: [
          { id: 'default-skill-1', name: '', category: 'web-development' as const },
          { id: 'default-skill-2', name: '', category: 'web-development' as const },
          { id: 'default-skill-3', name: '', category: 'web-development' as const }
        ]
      } as PortfolioData;
  }
};

// Backward compatibility
export const defaultPortfolioData = (portfolioId: string, userId: string, user: User): PortfolioData =>
  createDefaultPortfolioData(portfolioId, userId, user, 'general', 'general') as PortfolioData;


// Helper functions for user management
export const createDefaultUser = (firebaseUser: { uid: string; email: string; displayName?: string | null; photoURL?: string | null }): User => ({
  uid: firebaseUser.uid,
  email: firebaseUser.email,
  displayName: firebaseUser.displayName || firebaseUser.email.split('@')[0],
  photoURL: firebaseUser.photoURL || null,
  plan: 'free',
  createdAt: new Date(),
  updatedAt: new Date(),
  settings: {
    emailNotifications: true,
    publicProfile: true,
  },
});

// Check if user has premium features
export const hasPremiumAccess = (user: User): boolean => {
  if (user.plan !== 'premium') return false;
  if (!user.premiumExpiresAt) return false;
  return new Date() < user.premiumExpiresAt;
};

// Premium feature checks
export const canExportPortfolio = (user: User): boolean => hasPremiumAccess(user);
export const canUsePremiumThemes = (user: User): boolean => hasPremiumAccess(user);
export const getMaxPortfolios = (user: User): number => hasPremiumAccess(user) ? 10 : 1;

// A type for upload mutations, makes passing it easier
export type UploadFunction = (vars: { file: File; type: 'profile' | 'resume' | 'project'; id?: string }) => void;

// Theme Props - Updated to support any portfolio data type
export interface ProfolifyThemeProps {
  isEditing: boolean;
  serverData?: AnyPortfolioData; // For public, server-rendered pages
  onImageUpload?: UploadFunction; // For editor page
}

// Props for each section component
export interface SectionProps {
  isEditing: boolean;
  serverData?: AnyPortfolioData;
  onImageUpload?: UploadFunction;
}

// Type guards for portfolio data types
export const isGeneralPortfolio = (data: AnyPortfolioData): data is PortfolioData =>
  data.themeCategory === 'general' && data.professionType === 'general';

export const isMedicalPortfolio = (data: AnyPortfolioData): data is MedicalPortfolioData =>
  data.themeCategory === 'healthcare' && (data.professionType === 'doctor' || data.professionType === 'nurse');

export const isITPortfolio = (data: AnyPortfolioData): data is ITPortfolioData =>
  data.themeCategory === 'technology' && data.professionType === 'it-professional';

export const isCreativePortfolio = (data: AnyPortfolioData): data is CreativePortfolioData =>
  data.themeCategory === 'creative' && (data.professionType === 'photographer' || data.professionType === 'designer');

export const isTravelPortfolio = (data: AnyPortfolioData): data is TravelPortfolioData =>
  data.themeCategory === 'creative' && data.professionType === 'traveller';

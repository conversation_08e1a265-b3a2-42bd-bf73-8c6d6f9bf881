// import { doc, getDoc, setDoc, updateDoc, deleteDoc, collection, query, where, getDocs } from 'firebase/firestore';
// import { deleteUser as deleteFirebaseUser, User as FirebaseUser } from 'firebase/auth';
// import { firestore } from './firebase';
// import { User, createDefaultUser } from './types';
// import { getUserPortfolios } from './portfolio-api';
import { doc, getDoc, setDoc, updateDoc, deleteDoc, collection, query, where, getDocs } from 'firebase/firestore';
import { deleteUser as deleteFirebaseUser, User as FirebaseUser } from 'firebase/auth';
import { firestore } from './firebase';
import { User, createDefaultUser } from './types';
import { getUserPortfolios } from './portfolio-api';

// Migration function to handle old users who have portfolios but no user record
export const migrateOldUser = async (firebaseUser: FirebaseUser): Promise<User | null> => {
  try {
    // Check if user has portfolios
    const portfolios = await getUserPortfolios(firebaseUser.uid);

    if (portfolios.length > 0) {
      console.log(`Migrating old user ${firebaseUser.uid} with ${portfolios.length} portfolios`);

      // Create user record from Firebase user data
      const userData = {
        uid: firebaseUser.uid,
        email: firebaseUser.email!,
        displayName: firebaseUser.displayName || firebaseUser.email!.split('@')[0],
        photoURL: firebaseUser.photoURL || undefined,
      };

      // Create user record
      const user = await createOrUpdateUser(userData);
      console.log('Successfully migrated old user:', user.uid);
      return user;
    }

    return null;
  } catch (error) {
    console.error('Error migrating old user:', error);
    return null;
  }
};

// Get user by ID
export const getUser = async (uid: string): Promise<User | null> => {
  try {
    const docRef = doc(firestore, 'users', uid);
    const docSnap = await getDoc(docRef);

    if (docSnap.exists()) {
      const data = docSnap.data();
      return {
        ...data,
        createdAt: data.createdAt?.toDate() || new Date(),
        updatedAt: data.updatedAt?.toDate() || new Date(),
        premiumExpiresAt: data.premiumExpiresAt?.toDate(),
      } as User;
    }

    return null;
  } catch (error) {
    console.error('Error getting user:', error);
    throw error;
  }
};

// Create or update user (called after Google Auth)
export const createOrUpdateUser = async (firebaseUser: { uid: string; email: string; displayName?: string; photoURL?: string }): Promise<User> => {
  try {
    const userRef = doc(firestore, 'users', firebaseUser.uid);
    const existingUser = await getDoc(userRef);
    
    if (existingUser.exists()) {
      // Update existing user with latest info from Firebase Auth
      const userData = existingUser.data() as User;
      const updatedUser = {
        ...userData,
        email: firebaseUser.email,
        displayName: firebaseUser.displayName || userData.displayName,
        photoURL: firebaseUser.photoURL || userData.photoURL,
        updatedAt: new Date(),
      };
      
      await updateDoc(userRef, {
        email: updatedUser.email,
        displayName: updatedUser.displayName,
        photoURL: updatedUser.photoURL,
        updatedAt: updatedUser.updatedAt,
      });
      
      return updatedUser;
    } else {
      // Create new user
      const newUser = createDefaultUser(firebaseUser);
      await setDoc(userRef, newUser);
      return newUser;
    }
  } catch (error) {
    console.error('Error creating/updating user:', error);
    throw error;
  }
};

// Update user profile
export const updateUserProfile = async (uid: string, updates: Partial<Pick<User, 'displayName' | 'settings'>>): Promise<void> => {
  try {
    const userRef = doc(firestore, 'users', uid);
    await updateDoc(userRef, {
      ...updates,
      updatedAt: new Date(),
    });
  } catch (error) {
    console.error('Error updating user profile:', error);
    throw error;
  }
};

// Upgrade user to premium
export const upgradeUserToPremium = async (uid: string, expiresAt: Date): Promise<void> => {
  try {
    const userRef = doc(firestore, 'users', uid);
    await updateDoc(userRef, {
      plan: 'premium',
      premiumExpiresAt: expiresAt,
      updatedAt: new Date(),
    });
  } catch (error) {
    console.error('Error upgrading user to premium:', error);
    throw error;
  }
};

// Delete user account (cascade delete portfolios)
export const deleteUserAccount = async (uid: string, firebaseUser: FirebaseUser): Promise<void> => {
  try {
    // Delete all user's portfolios
    const portfoliosQuery = query(collection(firestore, 'portfolios'), where('userId', '==', uid));
    const portfoliosSnapshot = await getDocs(portfoliosQuery);
    const deletePromises = portfoliosSnapshot.docs.map(doc => deleteDoc(doc.ref));
    await Promise.all(deletePromises);
    // Delete user document
    await deleteDoc(doc(firestore, 'users', uid));
    // Delete Firebase Auth user
    if (firebaseUser) {
      await deleteFirebaseUser(firebaseUser);
    }
  } catch (error) {
    console.error('Error deleting user account:', error);
    throw error;
  }
};
/**
 * Theme Management Utility
 * 
 * This utility provides functions for managing themes, including:
 * - CSS file synchronization between source and public directories
 * - Theme validation and registration
 * - Build-time theme processing
 */

import fs from 'fs';
import path from 'path';
import { THEME_REGISTRY, validateThemeConfig, getThemesByThemeCategory, getThemesByProfession, ThemeConfig } from '@/themes/theme-registry';
import { AnyPortfolioData } from './types';

/**
 * Sync CSS files from themes directory to public directory
 * This ensures the public CSS files are always up-to-date with source files
 */
export async function syncThemeCssFiles(): Promise<void> {
  console.log('🎨 Syncing theme CSS files...');
  
  for (const theme of THEME_REGISTRY) {
    try {
      const sourcePath = path.join(process.cwd(), theme.sourceCssFile);
      const publicPath = path.join(process.cwd(), 'public', theme.cssFile);
      
      // Ensure public directory exists
      const publicDir = path.dirname(publicPath);
      if (!fs.existsSync(publicDir)) {
        fs.mkdirSync(publicDir, { recursive: true });
      }
      
      // Copy source CSS to public directory
      if (fs.existsSync(sourcePath)) {
        fs.copyFileSync(sourcePath, publicPath);
        console.log(`✅ Synced ${theme.name}: ${theme.sourceCssFile} → public${theme.cssFile}`);
      } else {
        console.warn(`⚠️  Source CSS file not found for ${theme.name}: ${sourcePath}`);
      }
    } catch (error) {
      console.error(`❌ Failed to sync CSS for ${theme.name}:`, error);
    }
  }
  
  console.log('✨ Theme CSS sync completed!');
}

/**
 * Validate all registered themes
 */
export function validateAllThemes(): { valid: boolean; errors: Record<string, string[]> } {
  const errors: Record<string, string[]> = {};
  let hasErrors = false;
  
  for (const theme of THEME_REGISTRY) {
    const themeErrors = validateThemeConfig(theme);
    if (themeErrors.length > 0) {
      errors[theme.id] = themeErrors;
      hasErrors = true;
    }
  }
  
  return { valid: !hasErrors, errors };
}

/**
 * Get theme file structure for a given theme ID
 */
export function getThemeStructure(themeId: string): {
  themeDir: string;
  componentsDir: string;
  cssFile: string;
  publicCssFile: string;
} | null {
  const theme = THEME_REGISTRY.find(t => t.id === themeId);
  if (!theme) return null;
  
  const themeDir = path.dirname(theme.sourceCssFile);
  
  return {
    themeDir,
    componentsDir: path.join(themeDir, 'components'),
    cssFile: theme.sourceCssFile,
    publicCssFile: path.join('public', theme.cssFile),
  };
}

/**
 * Create a new theme template structure
 */
export async function createThemeTemplate(
  themeId: string,
  themeName: string
): Promise<void> {
  const themeDir = `themes/${themeId}`;
  const componentsDir = `${themeDir}/components`;
  
  // Create directories
  if (!fs.existsSync(themeDir)) {
    fs.mkdirSync(themeDir, { recursive: true });
  }
  if (!fs.existsSync(componentsDir)) {
    fs.mkdirSync(componentsDir, { recursive: true });
  }
  
  // Create basic CSS file
  const cssContent = `/* ${themeName} Theme CSS */
/* Base styles and reset */
.theme-${themeId}-root {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
  line-height: 1.6;
  color: #111827;
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  background: white;
  overflow-x: hidden;
}

.theme-${themeId}-root *,
.theme-${themeId}-root *::before,
.theme-${themeId}-root *::after {
  box-sizing: inherit;
}

/* Add your theme styles here */
`;
  
  fs.writeFileSync(`${themeDir}/${themeId}.css`, cssContent);
  
  // Create basic theme component
  const componentContent = `"use client";
import { ProfolifyThemeProps } from "@/lib/types";

export function ${themeName.replace(/\s+/g, '')}Theme({ isEditing, serverData, onImageUpload }: ProfolifyThemeProps) {
    return (
        <div className="theme-${themeId}-root">
            {/* Add your theme components here */}
            <div className="theme-${themeId}-container">
                <h1>Welcome to ${themeName} Theme</h1>
                <p>Start building your theme components here!</p>
            </div>
        </div>
    );
}
`;
  
  fs.writeFileSync(`${componentsDir}/${themeName.replace(/\s+/g, '')}Theme.tsx`, componentContent);
  
  console.log(`✅ Created theme template: ${themeId}`);
  console.log(`📁 Theme directory: ${themeDir}`);
  console.log(`🎨 CSS file: ${themeDir}/${themeId}.css`);
  console.log(`⚛️  Component: ${componentsDir}/${themeName.replace(/\s+/g, '')}Theme.tsx`);
  console.log(`\n📝 Next steps:`);
  console.log(`1. Add your theme to themes/theme-registry.ts`);
  console.log(`2. Build your theme components`);
  console.log(`3. Run 'npm run sync-themes' to sync CSS files`);
}

/**
 * Check if theme CSS files are in sync
 */
export function checkThemeSyncStatus(): { inSync: boolean; outdated: string[] } {
  const outdated: string[] = [];
  
  for (const theme of THEME_REGISTRY) {
    try {
      const sourcePath = path.join(process.cwd(), theme.sourceCssFile);
      const publicPath = path.join(process.cwd(), 'public', theme.cssFile);
      
      if (!fs.existsSync(sourcePath) || !fs.existsSync(publicPath)) {
        outdated.push(theme.id);
        continue;
      }
      
      const sourceStats = fs.statSync(sourcePath);
      const publicStats = fs.statSync(publicPath);
      
      if (sourceStats.mtime > publicStats.mtime) {
        outdated.push(theme.id);
      }
    } catch {
      outdated.push(theme.id);
    }
  }
  
  return { inSync: outdated.length === 0, outdated };
}

/**
 * Get recommended themes for a portfolio based on its profession type
 */
export function getRecommendedThemes(portfolioData: AnyPortfolioData): ThemeConfig[] {
  // First try to get themes for the specific profession
  const professionThemes = getThemesByProfession(portfolioData.professionType);

  if (professionThemes.length > 0) {
    return professionThemes;
  }

  // Fallback to category-based themes
  const categoryThemes = getThemesByThemeCategory(portfolioData.themeCategory);

  if (categoryThemes.length > 0) {
    return categoryThemes;
  }

  // Final fallback to general themes
  return getThemesByThemeCategory('general');
}

/**
 * Check if a theme is compatible with a portfolio type
 */
export function isThemeCompatible(themeId: string, portfolioData: AnyPortfolioData): boolean {
  const theme = THEME_REGISTRY.find(t => t.id === themeId);

  if (!theme) return false;

  // Check if the theme supports the portfolio's profession type
  return theme.professionTypes.includes(portfolioData.professionType) ||
         theme.themeCategory === portfolioData.themeCategory ||
         theme.themeCategory === 'general';
}

/**
 * Get theme compatibility info
 */
export function getThemeCompatibilityInfo(themeId: string, portfolioData: AnyPortfolioData): {
  compatible: boolean;
  reason?: string;
  recommendation?: string;
} {
  const theme = THEME_REGISTRY.find(t => t.id === themeId);

  if (!theme) {
    return {
      compatible: false,
      reason: 'Theme not found',
    };
  }

  const isCompatible = isThemeCompatible(themeId, portfolioData);

  if (isCompatible) {
    return { compatible: true };
  }

  // Provide specific compatibility feedback
  if (!theme.professionTypes.includes(portfolioData.professionType)) {
    const recommendedThemes = getRecommendedThemes(portfolioData);
    return {
      compatible: false,
      reason: `This theme is designed for ${theme.professionTypes.join(', ')} but your portfolio is for ${portfolioData.professionType}`,
      recommendation: recommendedThemes.length > 0
        ? `Consider using: ${recommendedThemes.map(t => t.name).join(', ')}`
        : 'No specific themes available for your profession type'
    };
  }

  return {
    compatible: false,
    reason: 'Theme category mismatch',
  };
}

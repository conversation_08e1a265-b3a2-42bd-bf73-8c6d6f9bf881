import admin from 'firebase-admin';

// --- THIS IS THE FIX ---
// We no longer import a local JSON file.

if (!process.env.FIREBASE_SERVICE_ACCOUNT_BASE64) {
    throw new Error('FIREBASE_SERVICE_ACCOUNT_BASE64 environment variable is not set. Please encode your service account key to Base64 and set it.');
}

const decodedServiceAccount = Buffer.from(
    process.env.FIREBASE_SERVICE_ACCOUNT_BASE64,
    'base64'
).toString('utf-8');

const serviceAccount = JSON.parse(decodedServiceAccount);

// Check if the app is already initialized to prevent errors.
if (!admin.apps.length) {
  admin.initializeApp({
    credential: admin.credential.cert(serviceAccount),
  });
}

const auth = admin.auth();
export { auth };
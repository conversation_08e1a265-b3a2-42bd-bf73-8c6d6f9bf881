/**
 * Live DOM Capture Export System
 * 
 * This system captures the actual rendered HTML from the live portfolio page,
 * ensuring perfect layout preservation for any theme.
 */

import { AnyPortfolioData } from './types';
import { getThemeCssUrl } from '@/themes/theme-registry';
import JSZip from 'jszip';

/**
 * Validate CSS content to ensure it's not empty or corrupted
 */
function validateCssContent(css: string, themeId: string): boolean {
  if (!css || css.trim().length === 0) {
    console.error(`❌ CSS is empty for theme: ${themeId}`);
    return false;
  }

  // Check for basic CSS structure
  if (!css.includes('{') || !css.includes('}')) {
    console.error(`❌ CSS appears to be corrupted for theme: ${themeId}`);
    return false;
  }

  // Check for theme-specific classes
  const expectedClasses = ['theme-', 'navbar', 'hero', 'about'];
  const hasExpectedClasses = expectedClasses.some(className => css.includes(className));

  if (!hasExpectedClasses) {
    console.warn(`⚠️ CSS may not contain expected theme classes for: ${themeId}`);
  }

  console.log(`✅ CSS validation passed for theme: ${themeId}`);
  return true;
}

/**
 * Capture DOM from live portfolio page
 */
async function captureLivePortfolioDOM(portfolioData: AnyPortfolioData): Promise<string> {
  return new Promise((resolve, reject) => {
    try {
      console.log('🎨 Creating hidden iframe to capture live portfolio...');
      
      // Create hidden iframe
      const iframe = document.createElement('iframe');
      iframe.style.position = 'absolute';
      iframe.style.left = '-9999px';
      iframe.style.top = '-9999px';
      iframe.style.width = '1200px';
      iframe.style.height = '800px';
      iframe.style.visibility = 'hidden';
      iframe.style.border = 'none';
      
      // Set up iframe load handler
      iframe.onload = () => {
        try {
          console.log('✅ Iframe loaded, capturing DOM...');
          
          // Wait a bit for any dynamic content to load
          setTimeout(() => {
            try {
              const iframeDoc = iframe.contentDocument || iframe.contentWindow?.document;
              if (!iframeDoc) {
                throw new Error('Cannot access iframe document');
              }

              // Find the theme root element - try multiple selectors
              let themeRoot = iframeDoc.querySelector('[class*="theme-"][class*="-root"]');

              // Fallback: look for any element with theme class
              if (!themeRoot) {
                themeRoot = iframeDoc.querySelector('[class*="theme-"]');
              }

              // Fallback: look for main content container
              if (!themeRoot) {
                themeRoot = iframeDoc.querySelector('main, .portfolio-content, #portfolio-content');
              }

              // Last resort: get body content
              if (!themeRoot) {
                themeRoot = iframeDoc.body;
              }

              if (!themeRoot) {
                throw new Error('No suitable content found in iframe');
              }

              console.log('✅ Found theme element:', themeRoot.className || themeRoot.tagName);

              // Clone the theme element
              const clonedTheme = themeRoot.cloneNode(true) as HTMLElement;

              // Clean up editing elements and fix images
              cleanDOMForExport(clonedTheme);

              // Additional image fixing for iframe capture
              fixNextJSImagesInDOM(clonedTheme);

              // Ensure mobile menu attributes are set for export
              ensureMobileMenuAttributes(clonedTheme);

              // Final check: ensure all images are properly fixed
              console.log('🔍 Final image check before generating HTML (iframe)...');
              const finalImages = clonedTheme.querySelectorAll('img');
              finalImages.forEach((img, index) => {
                console.log(`🔍 Final image ${index + 1} (iframe):`, img.src);
                if (img.src && img.src.includes('/_next/image')) {
                  console.error('❌ STILL FOUND NEXT.JS IMAGE URL (iframe):', img.src);
                  // Force fix it one more time
                  try {
                    const urlParts = img.src.split('?');
                    if (urlParts.length > 1) {
                      const urlParams = new URLSearchParams(urlParts[1]);
                      const originalUrl = urlParams.get('url');
                      if (originalUrl) {
                        img.src = decodeURIComponent(originalUrl);
                        console.log('🔧 FORCE FIXED (iframe):', img.src);
                      }
                    }
                  } catch (error) {
                    console.error('❌ Force fix failed (iframe):', error);
                    img.src = 'https://placehold.co/400x300/f3f4f6/6b7280?text=Image';
                  }
                }
              });

              // Get the HTML
              const capturedHTML = clonedTheme.outerHTML;
              
              // Clean up iframe safely
              try {
                if (iframe.parentNode) {
                  iframe.parentNode.removeChild(iframe);
                } else {
                  console.warn('⚠️ Iframe has no parent node, skipping removal');
                }
              } catch (error) {
                console.warn('⚠️ Failed to remove iframe:', error);
              }
              
              console.log('✅ DOM captured successfully:', capturedHTML.length, 'characters');
              resolve(capturedHTML);
              
            } catch (error) {
              try {
                if (iframe.parentNode) {
                  iframe.parentNode.removeChild(iframe);
                }
              } catch (cleanupError) {
                console.warn('⚠️ Failed to cleanup iframe:', cleanupError);
              }
              reject(error);
            }
          }, 5000); // Wait 5 seconds for content to load
          
        } catch (error) {
          try {
            if (iframe.parentNode) {
              iframe.parentNode.removeChild(iframe);
            }
          } catch (cleanupError) {
            console.warn('⚠️ Failed to cleanup iframe:', cleanupError);
          }
          reject(error);
        }
      };
      
      iframe.onerror = () => {
        try {
          if (iframe.parentNode) {
            iframe.parentNode.removeChild(iframe);
          }
        } catch (error) {
          console.warn('⚠️ Failed to remove iframe on error:', error);
        }
        reject(new Error('Failed to load iframe'));
      };

      // Add iframe to document with error handling
      try {
        document.body.appendChild(iframe);
      } catch (error) {
        console.error('❌ Failed to append iframe:', error);
        reject(new Error('Failed to create export iframe'));
        return;
      }
      
      // Navigate to the portfolio page
      const portfolioUrl = portfolioData.slug
        ? `${window.location.origin}/${portfolioData.slug}`
        : `${window.location.origin}/portfolio`;
        
      console.log('🌐 Loading portfolio URL:', portfolioUrl);
      iframe.src = portfolioUrl;
      
    } catch (error) {
      reject(error);
    }
  });
}

/**
 * Fix Next.js image URLs in HTML string - Enhanced version
 */
function fixNextJSImageURLsInHTML(html: string): string {
  console.log('🔧 Fixing Next.js image URLs in HTML string...');

  // Multiple regex patterns to catch different Next.js image URL formats
  const patterns = [
    /src="[^"]*\/_next\/image\?[^"]*"/g,
    /src='[^']*\/_next\/image\?[^']*'/g,
    /src=[^>\s]*\/_next\/image\?[^>\s]*/g
  ];

  let fixedHtml = html;

  patterns.forEach((pattern, index) => {
    console.log(`🔧 Applying pattern ${index + 1}:`, pattern);

    fixedHtml = fixedHtml.replace(pattern, (match) => {
      console.log('🔧 Found Next.js image URL in HTML:', match);

      try {
        // Extract the URL from the match - handle both quoted and unquoted
        let srcMatch = match.match(/src="([^"]*)"/);
        if (!srcMatch) {
          srcMatch = match.match(/src='([^']*)'/);
        }
        if (!srcMatch) {
          srcMatch = match.match(/src=([^>\s]*)/);
        }

        if (srcMatch) {
          const fullUrl = srcMatch[1];
          console.log('🔧 Extracted URL:', fullUrl);

          // Handle URL-encoded URLs
          let decodedFullUrl = fullUrl;
          try {
            decodedFullUrl = decodeURIComponent(fullUrl);
          } catch {
            // URL might not be encoded
          }

          const urlParts = decodedFullUrl.split('?');

          if (urlParts.length > 1) {
            const urlParams = new URLSearchParams(urlParts[1]);
            const originalUrl = urlParams.get('url');

            if (originalUrl) {
              const decodedUrl = decodeURIComponent(originalUrl);
              const quote = match.includes('"') ? '"' : (match.includes("'") ? "'" : '');
              const fixedMatch = `src=${quote}${decodedUrl}${quote}`;
              console.log('✅ Fixed HTML image URL:', fixedMatch);
              return fixedMatch;
            }
          }
        }
      } catch (error) {
        console.warn('❌ Failed to fix HTML image URL:', match, error);
      }

      // Fallback to placeholder
      const quote = match.includes('"') ? '"' : (match.includes("'") ? "'" : '');
      return `src=${quote}https://placehold.co/400x300/f3f4f6/6b7280?text=Image${quote}`;
    });
  });

  // Count how many fixes were made
  const originalMatches = (html.match(/\/_next\/image/g) || []).length;
  const remainingMatches = (fixedHtml.match(/\/_next\/image/g) || []).length;
  console.log(`🔧 Fixed ${originalMatches - remainingMatches} out of ${originalMatches} Next.js image URLs`);

  return fixedHtml;
}

/**
 * Fix Next.js optimized images in DOM
 */
function fixNextJSImagesInDOM(element: HTMLElement): void {
  const images = element.querySelectorAll('img');
  console.log(`🖼️ Found ${images.length} images to process`);

  images.forEach((img, index) => {
    console.log(`🖼️ Processing image ${index + 1}:`, img.src);

    if (img.src && img.src.includes('/_next/image')) {
      console.log('🔧 Found Next.js optimized image, fixing...');
      try {
        // Extract the original URL from Next.js image optimization URL
        const urlParts = img.src.split('?');
        if (urlParts.length > 1) {
          const urlParams = new URLSearchParams(urlParts[1]);
          const originalUrl = urlParams.get('url');
          if (originalUrl) {
            const decodedUrl = decodeURIComponent(originalUrl);
            img.src = decodedUrl;
            console.log('✅ Fixed Next.js image URL:', decodedUrl);
          } else {
            console.warn('⚠️ No URL parameter found in:', img.src);
            img.src = 'https://placehold.co/400x300/f3f4f6/6b7280?text=Image';
          }
        } else {
          console.warn('⚠️ No query parameters found in:', img.src);
          img.src = 'https://placehold.co/400x300/f3f4f6/6b7280?text=Image';
        }
      } catch (error) {
        console.warn('❌ Failed to parse Next.js image URL:', img.src, error);
        img.src = 'https://placehold.co/400x300/f3f4f6/6b7280?text=Image';
      }
    } else if (img.src && img.src.startsWith('blob:')) {
      console.log('🔧 Found blob URL, replacing with placeholder');
      img.src = 'https://placehold.co/400x300/f3f4f6/6b7280?text=Image';
    } else {
      console.log('✅ Image URL is already valid:', img.src);
    }
  });
}

/**
 * Ensure mobile menu attributes are properly set for export
 */
function ensureMobileMenuAttributes(element: HTMLElement): void {
  // Find mobile menu buttons and ensure they have the right attributes
  const mobileButtons = element.querySelectorAll('.theme-modern-navbar-mobile-btn, .theme-creative-navbar-mobile-btn');

  mobileButtons.forEach(button => {
    if (!button.getAttribute('data-mobile-menu-toggle')) {
      button.setAttribute('data-mobile-menu-toggle', 'true');

      // Determine target based on theme
      if (button.classList.contains('theme-modern-navbar-mobile-btn')) {
        button.setAttribute('data-target', 'modern-mobile-menu');
      } else if (button.classList.contains('theme-creative-navbar-mobile-btn')) {
        button.setAttribute('data-target', 'creative-mobile-menu');
      }

      console.log('📱 Added mobile menu attributes to button:', button.className);
    }
  });

  // Find mobile menus and ensure they have proper IDs
  const modernMenu = element.querySelector('.theme-modern-navbar-mobile-menu');
  if (modernMenu && !modernMenu.id) {
    modernMenu.id = 'modern-mobile-menu';
    console.log('📱 Added ID to modern mobile menu');
  }

  const creativeMenu = element.querySelector('.theme-creative-navbar-mobile-menu');
  if (creativeMenu && !creativeMenu.id) {
    creativeMenu.id = 'creative-mobile-menu';
    console.log('📱 Added ID to creative mobile menu');
  }
}

/**
 * Clean DOM elements for static export
 */
function cleanDOMForExport(element: HTMLElement): void {
  // Remove editing attributes
  const editingElements = element.querySelectorAll('[contenteditable], [data-editing]');
  editingElements.forEach(el => {
    el.removeAttribute('contenteditable');
    el.removeAttribute('data-editing');
    if (el.classList.contains('editing')) {
      el.classList.remove('editing');
    }
  });

  // Remove loading states
  const loadingElements = element.querySelectorAll('.loading, .spinner, [data-loading]');
  loadingElements.forEach(el => el.remove());

  // Remove edit controls
  const editControls = element.querySelectorAll('[data-edit], .edit-btn, .add-btn, .delete-btn');
  editControls.forEach(el => el.remove());

  // Remove upload inputs
  const uploadInputs = element.querySelectorAll('input[type="file"]');
  uploadInputs.forEach(el => el.remove());

  // Remove upload overlays
  const uploadOverlays = element.querySelectorAll('[class*="upload-overlay"]');
  uploadOverlays.forEach(el => el.remove());

  // Fix image sources for static export - this is the main image fixing function
  console.log('🖼️ Starting main image fixing process...');
  const images = element.querySelectorAll('img');
  console.log(`🖼️ Found ${images.length} images in cleanDOMForExport`);

  images.forEach((img, index) => {
    const originalSrc = img.src;
    console.log(`🖼️ Processing image ${index + 1} in cleanDOMForExport:`, originalSrc);

    // Handle different types of problematic image URLs
    if (img.src) {
      // Replace Next.js optimized image URLs (/_next/image) with original URLs
      if (img.src.includes('/_next/image')) {
        console.log('🔧 Found Next.js image in cleanDOMForExport, fixing...');
        try {
          // Extract the original URL from Next.js image optimization URL
          const urlParts = img.src.split('?');
          if (urlParts.length > 1) {
            const urlParams = new URLSearchParams(urlParts[1]);
            const originalUrl = urlParams.get('url');
            if (originalUrl) {
              img.src = decodeURIComponent(originalUrl);
              console.log('✅ Fixed Next.js image URL in cleanDOMForExport:', img.src);
            } else {
              console.warn('⚠️ No URL parameter found in cleanDOMForExport:', img.src);
              img.src = 'https://placehold.co/400x300/f3f4f6/6b7280?text=Image';
            }
          } else {
            console.warn('⚠️ No query parameters in cleanDOMForExport:', img.src);
            img.src = 'https://placehold.co/400x300/f3f4f6/6b7280?text=Image';
          }
        } catch (error) {
          console.warn('❌ Failed to parse Next.js image URL in cleanDOMForExport:', img.src, error);
          img.src = 'https://placehold.co/400x300/f3f4f6/6b7280?text=Image';
        }
      }
      // Replace blob URLs (temporary upload URLs)
      else if (img.src.startsWith('blob:')) {
        console.log('🔧 Found blob URL in cleanDOMForExport, replacing...');
        img.src = 'https://placehold.co/400x300/f3f4f6/6b7280?text=Image';
      }
      // Handle Firebase URLs that might not work in static exports
      else if (img.src.includes('firebasestorage.googleapis.com')) {
        console.log('✅ Found Cloudinary URL in cleanDOMForExport, keeping:', img.src);
        // Keep Firebase URLs as they should work in static exports
        // But add error handling
        img.onerror = function() {
          this.src = 'https://placehold.co/400x300/f3f4f6/6b7280?text=Image';
        };
      }
      // Handle relative URLs by making them absolute
      else if (img.src.startsWith('/') && !img.src.startsWith('//')) {
        console.log('🔧 Found relative URL in cleanDOMForExport, making absolute...');
        img.src = window.location.origin + img.src;
      }
      else {
        console.log('✅ Image URL already valid in cleanDOMForExport:', img.src);
      }
    } else {
      // No src attribute, add placeholder
      console.log('⚠️ No src attribute found in cleanDOMForExport, adding placeholder');
      img.src = 'https://placehold.co/400x300/f3f4f6/6b7280?text=Image';
    }

    // Ensure images have proper alt text for accessibility
    if (!img.alt) {
      img.alt = 'Portfolio image';
    }

    // Add loading optimization and error handling
    img.loading = 'lazy';

    // Add inline error handling for static export
    if (!img.onerror) {
      img.onerror = function() {
        this.src = 'https://placehold.co/400x300/f3f4f6/6b7280?text=Image';
      };
    }

    console.log(`🖼️ Final image ${index + 1} src:`, img.src);
  });

  console.log('🖼️ Completed main image fixing process');
}

/**
 * Export portfolio using live DOM capture
 */
export async function exportWithLiveDOMCapture(portfolioData: unknown): Promise<boolean> {
  try {
    const data = portfolioData as AnyPortfolioData; // Type assertion for unknown data
    console.log('🎨 Starting live DOM capture export for:', data.templateId);

    // Set global export flag to help components detect export context
    (window as typeof window & { __PORTFOLIO_EXPORT__?: boolean }).__PORTFOLIO_EXPORT__ = true;

    // Also set data attribute on document for additional detection
    document.documentElement.setAttribute('data-export', 'true');

    // Force a small delay to allow React components to re-render with export context
    await new Promise(resolve => setTimeout(resolve, 100));

    // Check if we're on a page where we can capture the portfolio
    const currentPath = window.location.pathname;
    const isOnPortfolioPage = currentPath.includes('/portfolio') || currentPath === `/${data.slug}`;
    
    let capturedHTML: string;
    
    if (isOnPortfolioPage) {
      console.log('✅ On portfolio page, capturing current DOM...');

      // We're on the portfolio page, capture current DOM
      let themeRoot = document.querySelector('[class*="theme-"][class*="-root"]');

      // Fallback: look for any element with theme class
      if (!themeRoot) {
        themeRoot = document.querySelector('[class*="theme-"]');
      }

      // Fallback: look for main content container
      if (!themeRoot) {
        themeRoot = document.querySelector('main, .portfolio-content, #portfolio-content');
      }

      if (!themeRoot) {
        throw new Error('No suitable theme content found on current page');
      }

      console.log('✅ Found theme element on current page:', themeRoot.className || themeRoot.tagName);

      const clonedTheme = themeRoot.cloneNode(true) as HTMLElement;
      cleanDOMForExport(clonedTheme);

      // Additional image fixing for current page capture
      fixNextJSImagesInDOM(clonedTheme);

      // Ensure mobile menu attributes are set for export
      ensureMobileMenuAttributes(clonedTheme);

      // Final check: ensure all images are properly fixed
      console.log('🔍 Final image check before generating HTML...');
      const finalImages = clonedTheme.querySelectorAll('img');
      finalImages.forEach((img, index) => {
        console.log(`🔍 Final image ${index + 1}:`, img.src);
        if (img.src && img.src.includes('/_next/image')) {
          console.error('❌ STILL FOUND NEXT.JS IMAGE URL:', img.src);
          // Force fix it one more time
          try {
            const urlParts = img.src.split('?');
            if (urlParts.length > 1) {
              const urlParams = new URLSearchParams(urlParts[1]);
              const originalUrl = urlParams.get('url');
              if (originalUrl) {
                img.src = decodeURIComponent(originalUrl);
                console.log('🔧 FORCE FIXED:', img.src);
              }
            }
          } catch (error) {
            console.error('❌ Force fix failed:', error);
            img.src = 'https://placehold.co/400x300/f3f4f6/6b7280?text=Image';
          }
        }
      });

      capturedHTML = clonedTheme.outerHTML;
      
    } else {
      console.log('📄 Not on portfolio page, using iframe capture...');
      
      // We're on dashboard or other page, use iframe capture
      capturedHTML = await captureLivePortfolioDOM(data);
    }

    // Get theme CSS with better error handling
    const cssUrl = getThemeCssUrl(data.templateId);
    if (!cssUrl) {
      throw new Error(`No CSS URL found for theme: ${data.templateId}`);
    }

    console.log('🎨 Fetching CSS from:', cssUrl);

    let cssContent = '';
    try {
      const response = await fetch(cssUrl);
      if (!response.ok) {
        throw new Error(`Failed to fetch CSS: ${response.status} ${response.statusText}`);
      }
      cssContent = await response.text();

      if (!validateCssContent(cssContent, data.templateId)) {
        throw new Error('CSS content validation failed');
      }

      console.log('✅ CSS loaded successfully:', cssContent.length, 'characters');
    } catch (error) {
      console.error('❌ CSS loading failed:', error);
      console.log('💡 Trying to sync themes...');

      // Try to sync themes
      try {
        const syncResponse = await fetch('/api/sync-themes', { method: 'POST' });
        if (syncResponse.ok) {
          console.log('✅ Themes synced, retrying CSS fetch...');

          // Retry CSS fetch after sync
          const retryResponse = await fetch(cssUrl);
          if (retryResponse.ok) {
            cssContent = await retryResponse.text();
            if (validateCssContent(cssContent, data.templateId)) {
              console.log('✅ CSS loaded after sync:', cssContent.length, 'characters');
            } else {
              cssContent = ''; // Reset if validation fails
            }
          }
        }
      } catch (syncError) {
        console.warn('⚠️ Theme sync failed:', syncError);
      }

      // If still no CSS, throw the original error
      if (!cssContent || cssContent.trim().length === 0) {
        throw new Error(`Failed to load CSS for theme ${data.templateId}. Please run "npm run sync-themes" and try again.`);
      }
    }

    // Fix any remaining Next.js image URLs in the HTML string
    let fixedHTML = fixNextJSImageURLsInHTML(capturedHTML);

    // Final aggressive fix: replace any remaining _next/image URLs
    const remainingNextImages = (fixedHTML.match(/\/_next\/image/g) || []).length;
    if (remainingNextImages > 0) {
      console.log(`⚠️ Still found ${remainingNextImages} Next.js image URLs, applying aggressive fix...`);

      // More aggressive regex to catch any remaining patterns
      fixedHTML = fixedHTML.replace(/[^"'>\s]*\/_next\/image\?[^"'<\s]*/g, (match) => {
        console.log('🔧 Aggressive fix for:', match);
        try {
          const urlMatch = match.match(/url=([^&]*)/);
          if (urlMatch) {
            const decodedUrl = decodeURIComponent(urlMatch[1]);
            console.log('✅ Aggressively fixed to:', decodedUrl);
            return decodedUrl;
          }
        } catch (error) {
          console.warn('❌ Aggressive fix failed:', error);
        }
        return 'https://placehold.co/400x300/f3f4f6/6b7280?text=Image';
      });

      const finalCount = (fixedHTML.match(/\/_next\/image/g) || []).length;
      console.log(`🔧 After aggressive fix: ${finalCount} Next.js URLs remaining`);
    }

    // Generate complete HTML document
    const completeHTML = generateCompleteHTMLDocument(data, fixedHTML, cssContent);

    // Create ZIP file with just the essentials
    const zip = new JSZip();
    zip.file('index.html', completeHTML);
    
    const readme = `# ${data.userName || 'Portfolio'} - Static Export

This portfolio was exported from Profolify with perfect layout preservation.

## Files:
- **index.html** - Your complete portfolio website (ready to upload!)
- **README.md** - This file

## How to Use:
1. Upload index.html to any web hosting service
2. That's it! Your portfolio is live

## Hosting Options:
- **Netlify**: Drag & drop index.html
- **Vercel**: Upload the file
- **GitHub Pages**: Commit to repository
- **Any Web Host**: Upload via FTP/control panel

## Details:
- Theme: ${data.templateId}
- Generated: ${new Date().toISOString()}
- Exported from: https://profolify.com

## Need Changes?
Visit https://profolify.com to edit and re-export your portfolio anytime!
`;
    
    zip.file('README.md', readme);

    // Generate and download ZIP
    const zipBlob = await zip.generateAsync({ type: 'blob' });
    
    const url = URL.createObjectURL(zipBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `${data.slug || 'portfolio'}.zip`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);

    console.log('✅ Live DOM capture export completed successfully');
    return true;

  } catch (error) {
    console.error('❌ Live DOM capture export failed:', error);
    throw error;
  } finally {
    // Clean up global export flag and data attribute
    delete (window as typeof window & { __PORTFOLIO_EXPORT__?: boolean }).__PORTFOLIO_EXPORT__;
    document.documentElement.removeAttribute('data-export');
  }
}

/**
 * Generate complete HTML document with captured DOM
 */
function generateCompleteHTMLDocument(
  portfolioData: AnyPortfolioData,
  capturedHTML: string,
  cssContent: string
): string {
  return `<!DOCTYPE html>
<html lang="en" data-export="true">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>${portfolioData.userName ? `${portfolioData.userName} - Portfolio` : 'Portfolio'}</title>
  <meta name="description" content="${portfolioData.about || 'Professional portfolio showcasing my work and experience'}">
  <meta name="author" content="${portfolioData.userName || 'Portfolio Owner'}">
  
  <!-- Open Graph / Facebook -->
  <meta property="og:type" content="website">
  <meta property="og:title" content="${portfolioData.userName ? `${portfolioData.userName} - Portfolio` : 'Portfolio'}">
  <meta property="og:description" content="${portfolioData.about || 'Professional portfolio showcasing my work and experience'}">
  ${portfolioData.profileImageUrl ? `<meta property="og:image" content="${portfolioData.profileImageUrl}">` : ''}
  
  <!-- Twitter -->
  <meta property="twitter:card" content="summary_large_image">
  <meta property="twitter:title" content="${portfolioData.userName ? `${portfolioData.userName} - Portfolio` : 'Portfolio'}">
  <meta property="twitter:description" content="${portfolioData.about || 'Professional portfolio showcasing my work and experience'}">
  ${portfolioData.profileImageUrl ? `<meta property="twitter:image" content="${portfolioData.profileImageUrl}">` : ''}
  
  <!-- Favicon -->
  <link rel="icon" type="image/x-icon" href="data:image/x-icon;base64,AAABAAEAEBAAAAEAIABoBAAAFgAAACgAAAAQAAAAIAAAAAEAIAAAAAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAA">
  
  <!-- Theme Styles -->
  <style>
    /* CSS Reset for exported site - Remove default browser margins/padding */
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    html, body {
      margin: 0 !important;
      padding: 0 !important;
      width: 100%;
      height: 100%;
    }

    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
      line-height: 1.6;
    }

    /* Theme-specific styles */
    ${cssContent}

    /* Export-specific optimizations */
    * {
      -webkit-print-color-adjust: exact !important;
      color-adjust: exact !important;
    }

    html {
      scroll-behavior: smooth;
    }

    img {
      max-width: 100%;
      height: auto;
    }

    /* Remove any loading states */
    .loading, .spinner, [data-loading] {
      display: none !important;
    }

    /* Ensure no unwanted margins on exported site */
    body {
      margin: 0 !important;
      padding: 0 !important;
    }

    /* Additional resets for common elements */
    h1, h2, h3, h4, h5, h6 {
      margin: 0;
      padding: 0;
    }

    p {
      margin: 0;
      padding: 0;
    }

    ul, ol {
      margin: 0;
      padding: 0;
      list-style: none;
    }

    a {
      text-decoration: none;
      color: inherit;
    }

    button {
      border: none;
      background: none;
      cursor: pointer;
    }

    /* Ensure theme containers take full width */
    .theme-modern-root,
    .theme-creative-root {
      width: 100%;
      margin: 0;
      padding: 0;
    }
  </style>
</head>
<body data-export="true">
  ${capturedHTML}
  
  <!-- Navigation Enhancement -->
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      // Smooth scroll for anchor links
      const links = document.querySelectorAll('a[href^="#"]');
      links.forEach(link => {
        link.addEventListener('click', function(e) {
          e.preventDefault();
          const target = document.querySelector(this.getAttribute('href'));
          if (target) {
            target.scrollIntoView({ behavior: 'smooth' });
          }
        });
      });

      // Mobile menu functionality for exported sites
      console.log('🔧 Setting up mobile menu functionality...');

      const mobileMenuToggles = document.querySelectorAll('[data-mobile-menu-toggle]');
      console.log('📱 Found mobile menu toggles:', mobileMenuToggles.length);

      mobileMenuToggles.forEach((toggle, index) => {
        const targetId = toggle.getAttribute('data-target');
        const mobileMenu = document.getElementById(targetId);

        console.log(\`📱 Toggle \${index + 1}: target=\${targetId}, menu found=\${!!mobileMenu}\`);

        if (mobileMenu) {
          // Ensure menu starts hidden
          mobileMenu.style.display = 'none';
          mobileMenu.classList.remove('active');

          toggle.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();

            console.log('📱 Mobile menu toggle clicked');

            // Toggle menu visibility
            const isVisible = mobileMenu.style.display !== 'none' && mobileMenu.style.display !== '';

            if (isVisible) {
              mobileMenu.style.display = 'none';
              mobileMenu.classList.remove('active');
              console.log('📱 Mobile menu closed');
            } else {
              mobileMenu.style.display = 'block';
              mobileMenu.classList.add('active');
              console.log('📱 Mobile menu opened');
            }
          });

          // Close menu when clicking on links
          const menuLinks = mobileMenu.querySelectorAll('a');
          console.log(\`📱 Found \${menuLinks.length} menu links\`);

          menuLinks.forEach(link => {
            link.addEventListener('click', function() {
              mobileMenu.style.display = 'none';
              mobileMenu.classList.remove('active');
              console.log('📱 Mobile menu closed via link click');
            });
          });
        }
      });

      // Close mobile menu when clicking outside
      document.addEventListener('click', function(e) {
        const mobileMenus = document.querySelectorAll('[id$="-mobile-menu"]');
        mobileMenus.forEach(menu => {
          const toggle = document.querySelector(\`[data-target="\${menu.id}"]\`);
          if (!menu.contains(e.target) && !toggle?.contains(e.target)) {
            menu.style.display = 'none';
            menu.classList.remove('active');
          }
        });
      });
    });
  </script>
</body>
</html>`;
}

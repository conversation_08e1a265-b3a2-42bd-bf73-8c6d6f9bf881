# Profolify – The Modern Portfolio Builder

<div align="center">
  <img src="./public/logo.png" alt="Profolify Logo" width="120" />
  <h1>Profolify</h1>
  <p>
    Create, edit, and export beautiful, professional portfolios in minutes.<br>
    No coding required. Pixel-perfect. Scalable. Extensible.
  </p>
  <a href="#"><strong>Try the Demo »</strong></a>
</div>

---

## 🚀 What is Profolify?

**Profolify** is a next-generation portfolio builder platform.  
It empowers anyone to create, edit, and publish stunning, responsive portfolios with real-time editing, professional themes, and one-click static export.

- **Live Editing:** WYSIWYG, inline, and instant.
- **Theme System:** Modular, extensible, and beautiful.
- **Pixel-Perfect Export:** Download your site as a static HTML package—what you see is what you get.
- **Secure & Scalable:** Built on Next.js, Firebase, and Vercel.

---

## ✨ Features

- **Google Authentication** – Secure, one-click sign-in.
- **Dashboard & Editor** – Modern UI for managing and editing portfolios.
- **Theme Marketplace** – Choose from (and extend) a growing library of themes.
- **Live DOM Capture Export** – Revolutionary export system for pixel-perfect static sites.
- **Cloudinary Integration** – Fast, optimized image delivery.
- **Mobile-First** – Every theme is fully responsive.
- **Account Management** – Profile, settings, and account deletion.
- **Default Content** – Instant onboarding with sample data.

---

## 🏗️ Architecture Overview

- **Frontend:** Next.js (App Router), Tailwind CSS, Shadcn/UI
- **Backend:** Firebase (Firestore, Auth), Cloudinary
- **Deployment:** Vercel (global CDN)
- **State Management:** TanStack Query, React Context + useReducer, Zustand
- **Export:** Client-side DOM capture, JSZip packaging

> For a deep dive, see [docs/ARCHITECTURE.md](docs/ARCHITECTURE.md)

---

## 🛠️ Quick Start

### Prerequisites

- Node.js v18+
- npm or yarn
- Git

### Installation

```bash
git clone <repository-url>
cd profolify
npm install
cp .env.example .env.local
# Edit .env.local with your Firebase, Cloudinary, etc. config
npm run sync-themes
npm run dev
```
import * as React from "react"
import { Slot } from "@radix-ui/react-slot"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"

const buttonVariants = cva(
  "inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium transition-all duration-200 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:ring-2 focus-visible:ring-brandPrimary focus-visible:ring-offset-2 focus-visible:ring-offset-backgroundPrimary rounded-lg",
  {
    variants: {
      variant: {
        // Primary solid button - clean and professional
        default:
          "bg-brandPrimary text-white hover:bg-brandPrimary/90 active:bg-brandPrimary/95 shadow-sm hover:shadow-md font-semibold",

        // Destructive action button - clean red
        destructive:
          "bg-error text-white hover:bg-error/90 active:bg-error/95 shadow-sm hover:shadow-md font-semibold",

        // Secondary button - clean neutral
        secondary:
          "bg-backgroundSecondary text-textPrimary hover:bg-backgroundTertiary border border-borderPrimary hover:border-borderSecondary shadow-sm hover:shadow-md font-medium",

        // Outline button - clean borders
        outline:
          "border-2 border-brandPrimary bg-transparent text-brandPrimary hover:bg-brandPrimary hover:text-white active:bg-brandPrimary/95 shadow-sm hover:shadow-md font-semibold",

        // Ghost button - minimal style
        ghost:
          "bg-transparent text-textPrimary hover:bg-backgroundSecondary hover:text-brandPrimary font-medium",

        // Link style button
        link:
          "text-brandPrimary underline-offset-4 hover:underline font-medium",

        // Plain text button
        plain:
          "bg-transparent text-textSecondary hover:text-brandPrimary font-medium",

        // Accent button - for special actions
        accent:
          "bg-brandAccent text-white hover:bg-brandAccent/90 active:bg-brandAccent/95 shadow-sm hover:shadow-md font-semibold",

        // Violet theme button
        violet:
          "bg-brandSecondary text-white hover:bg-brandSecondary/90 active:bg-brandSecondary/95 shadow-sm hover:shadow-md font-semibold",

        // Large CTA button - clean and prominent
        cta:
          "bg-brandPrimary text-white hover:bg-brandPrimary/90 active:bg-brandPrimary/95 shadow-lg hover:shadow-xl font-bold text-lg rounded-xl",

        // Glass effect button
        glass:
          "bg-white/10 backdrop-blur-md border border-white/20 text-textPrimary hover:bg-white/20 hover:shadow-lg font-medium",

        // Success button - clean green
        success:
          "bg-success text-white hover:bg-success/90 active:bg-success/95 shadow-sm hover:shadow-md font-semibold",

        // Warning button - clean amber
        warning:
          "bg-warning text-white hover:bg-warning/90 active:bg-warning/95 shadow-sm hover:shadow-md font-semibold",

        // Info button - clean blue
        info:
          "bg-info text-white hover:bg-info/90 active:bg-info/95 shadow-sm hover:shadow-md font-semibold"
      },
      size: {
        xs: "h-8 px-3 text-xs",
        sm: "h-9 px-4 text-sm",
        default: "h-11 px-6 text-sm",
        lg: "h-12 px-8 text-base",
        xl: "h-14 px-10 text-lg",
        icon: "h-10 w-10",
        "icon-sm": "h-8 w-8",
        "icon-lg": "h-12 w-12",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
)
export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
  VariantProps<typeof buttonVariants> {
  asChild?: boolean
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, asChild = false, ...props }, ref) => {
    const Comp = asChild ? Slot : "button"
    return (
      <Comp
        className={cn(buttonVariants({ variant, size, className }))}
        ref={ref}
        {...props}
      />
    )
  }
)
Button.displayName = "Button"

export { Button, buttonVariants }
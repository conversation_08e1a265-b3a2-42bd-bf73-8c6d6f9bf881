"use client";

import {
  Edit3,
  Share2,
  <PERSON>lette,
  Smartphone,
  ArrowRight,
  Sparkles,
  Globe,
  Download,
} from "lucide-react";
import { Button } from "../ui/button";
import { useRouter } from "next/navigation";

const features = [
  {
    icon: Edit3,
    title: "Free Portfolio Builder with WYSIWYG Editor",
    description:
      "Build professional portfolios with our intuitive drag-and-drop editor. Click directly on any text to edit in place. No coding skills required - perfect for developers, designers, and freelancers.",
    color: "from-violet-500 to-purple-500",
    highlight: "No Code Required",
  },
  {
    icon: Download,
    title: "Export Static HTML Files & Complete Websites",
    description:
      "Download your portfolio as complete static HTML files with embedded CSS. Perfect for hosting anywhere or integrating into existing websites. Revolutionary DOM capture technology ensures pixel-perfect exports.",
    color: "from-emerald-500 to-teal-500",
    highlight: "Static HTML Export",
  },
  {
    icon: Palette,
    title: "Professional Portfolio Themes & Templates",
    description:
      "Choose from stunning, mobile-responsive themes including Modern and Creative Minimalist designs. All themes are SEO-optimized and professionally crafted for maximum impact.",
    color: "from-purple-500 to-pink-500",
    highlight: "SEO-Optimized Themes",
  },
  {
    icon: Share2,
    title: "SEO-Optimized Portfolio URLs & Publishing",
    description:
      "Automatically generates clean, professional URLs from your name (e.g., /john-smith) for better search engine visibility. Instant publishing with optimized meta tags and structured data.",
    color: "from-blue-500 to-indigo-500",
    highlight: "SEO Optimized",
  },
  {
    icon: Smartphone,
    title: "Mobile-Responsive Portfolio Design",
    description:
      "All portfolio themes are fully responsive and optimized for mobile devices, tablets, and desktops. Your professional portfolio will look perfect on any screen size.",
    color: "from-orange-500 to-red-500",
    highlight: "Mobile Optimized",
  },
  {
    icon: Globe,
    title: "Professional Portfolio Hosting & Sharing",
    description:
      "Instantly publish your portfolio online with professional URLs. Share your work with clients, employers, and collaborators. Perfect for job applications and freelance projects.",
    color: "from-cyan-500 to-blue-500",
    highlight: "Instant Publishing",
  },
  // {
  //   icon: Sparkles,
  //   title: "Resume Builder & CV Portfolio Integration",
  //   description:
  //     "Create comprehensive portfolios that showcase your resume, projects, and skills in one place. Perfect for job seekers, freelancers, and professionals building their online presence.",
  //   color: "from-pink-500 to-rose-500",
  //   highlight: "Resume Integration",
  // },
  // {
  //   icon: Globe,
  //   title: "Instant Publishing",
  //   description:
  //     "Publish your portfolio to a live URL instantly. Share your professional presence with clients and employers immediately.",
  //   color: "from-cyan-500 to-blue-500",
  //   highlight: "One-Click Deploy",
  // },
];

export function FeaturesSection() {
  const router = useRouter();

  return (
    <section id="features" className="relative py-24 lg:py-32 overflow-hidden bg-backgroundSecondary">
      {/* Clean Background Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {/* Subtle accent orbs */}
        <div className="absolute top-20 right-10 w-64 h-64 bg-brandPrimary/3 rounded-full blur-3xl"></div>
        <div className="absolute bottom-20 left-10 w-80 h-80 bg-brandAccent/2 rounded-full blur-3xl"></div>
      </div>

      <div className="container relative mx-auto px-4 sm:px-6 lg:px-8">
        {/* Clean Section Header */}
        <div className="text-center mb-20">
          <div className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-brandPrimary/10 border border-brandPrimary/20 mb-8">
            <Sparkles className="w-4 h-4 text-brandPrimary" />
            <span className="text-sm font-semibold text-brandPrimary">
              Everything You Need
            </span>
          </div>

          <h2 className="text-4xl sm:text-5xl lg:text-6xl font-bold mb-6 leading-tight">
            <span className="gradient-text">
              Powerful Features
            </span>
            <br />
            <span className="text-textPrimary">
              Built for Professionals
            </span>
          </h2>

          <p className="text-lg md:text-xl text-textSecondary max-w-3xl mx-auto leading-relaxed">
            Create stunning portfolios with our intuitive editor, beautiful themes,
            and revolutionary export technology. Everything is free to get you started.
          </p>
        </div>

        {/* Clean Features Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-20">
          {features.map((feature, index) => {
            const IconComponent = feature.icon;
            // Clean color mapping for icons
            const iconColors = [
              'bg-brandPrimary',      // Indigo
              'bg-brandAccent',       // Emerald
              'bg-brandSecondary',    // Violet
              'bg-info',              // Blue
              'bg-warning',           // Amber
              'bg-success'            // Green
            ];
            const iconColor = iconColors[index % iconColors.length];

            return (
              <div
                key={feature.title}
                className="group p-8 rounded-2xl bg-backgroundSecondary/50 border border-borderPrimary hover:border-brandPrimary/30 transition-all duration-300 hover:shadow-lg hover:shadow-shadowAccent"
              >
                {/* Clean Icon */}
                <div className={`w-12 h-12 rounded-xl ${iconColor} flex items-center justify-center mb-6`}>
                  <IconComponent className="w-6 h-6 text-white" />
                </div>

                {/* Content */}
                <div className="space-y-4">
                  <h3 className="text-xl font-bold text-textPrimary">
                    {feature.title}
                  </h3>

                  <p className="text-textSecondary leading-relaxed">
                    {feature.description}
                  </p>

                  {/* Clean highlight badge */}
                  <div className="inline-flex items-center gap-2 px-3 py-1 rounded-full bg-brandPrimary/10 border border-brandPrimary/20">
                    <div className={`w-2 h-2 rounded-full ${iconColor}`}></div>
                    <span className="text-xs font-semibold text-brandPrimary">
                      {feature.highlight}
                    </span>
                  </div>
                </div>
              </div>
            );
          })}
        </div>

        {/* Clean CTA Section */}
        <div className="text-center bg-backgroundSecondary/30 rounded-2xl p-12 border border-borderPrimary">
          <h3 className="text-3xl md:text-4xl font-bold mb-4">
            <span className="gradient-text">Ready to Get Started?</span>
          </h3>

          <p className="text-lg text-textSecondary max-w-2xl mx-auto mb-8">
            Create your professional portfolio in minutes. Everything is free to get you started.
          </p>

          <Button
            variant="cta"
            size="lg"
            onClick={() => router.push("/login")}
            className="px-8 py-4"
          >
            <span className="flex items-center gap-2">
              Start Building Free
              <ArrowRight className="w-5 h-5" />
            </span>
          </Button>
        </div>
      </div>
    </section>
  );
}

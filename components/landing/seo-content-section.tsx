"use client";

import { 
  // Code, 
  // Palette, 
  Smartphone, 
  Search, 
  Download, 
  Zap,
  // Users,
  // Globe
} from "lucide-react";

const seoContent = [
  // {
  //   icon: Code,
  //   title: "Best Free Portfolio Builder for Developers",
  //   description: "Create stunning developer portfolios with code syntax highlighting, project showcases, and GitHub integration. Perfect for software engineers, web developers, and programmers looking to showcase their coding skills.",
  //   keywords: ["developer portfolio", "coding portfolio", "programmer portfolio", "software engineer portfolio"]
  // },
  // {
  //   icon: Palette,
  //   title: "Professional Portfolio Builder for Designers",
  //   description: "Showcase your creative work with beautiful, responsive portfolio themes designed specifically for graphic designers, UI/UX designers, and creative professionals. Display your projects in stunning galleries.",
  //   keywords: ["designer portfolio", "creative portfolio", "graphic design portfolio", "UI UX portfolio"]
  // },
  {
    icon: Download,
    title: "Export Portfolio as Static HTML Files",
    description: "Download your complete portfolio as static HTML, CSS, and JavaScript files. Host anywhere, no dependencies required. Perfect for developers who want full control over their portfolio hosting.",
    keywords: ["static HTML export", "portfolio HTML download", "static website generator", "HTML portfolio"]
  },
  {
    icon: Search,
    title: "SEO-Optimized Portfolio Websites",
    description: "All portfolios are automatically optimized for search engines with proper meta tags, structured data, and clean URLs. Get discovered by employers and clients through Google search.",
    keywords: ["SEO portfolio", "search optimized portfolio", "portfolio SEO", "discoverable portfolio"]
  },
  {
    icon: Smartphone,
    title: "Mobile-Responsive Portfolio Themes",
    description: "Every portfolio theme is fully responsive and mobile-optimized. Your portfolio will look perfect on smartphones, tablets, and desktop computers, ensuring a great user experience.",
    keywords: ["mobile portfolio", "responsive portfolio", "mobile-friendly portfolio", "tablet portfolio"]
  },
  // {
  //   icon: Users,
  //   title: "Portfolio Builder for Freelancers",
  //   description: "Perfect for freelancers who need to showcase their work to potential clients. Include testimonials, case studies, and contact forms to convert visitors into paying customers.",
  //   keywords: ["freelancer portfolio", "freelance portfolio", "client portfolio", "business portfolio"]
  // },
  // {
  //   icon: Globe,
  //   title: "Online Resume Builder & CV Portfolio",
  //   description: "Create comprehensive online resumes that go beyond traditional CVs. Showcase your work experience, skills, projects, and achievements in an interactive, professional format.",
  //   keywords: ["online resume", "CV portfolio", "resume website", "digital resume", "resume builder"]
  // },
  {
    icon: Zap,
    title: "Quick Portfolio Creation in Minutes",
    description: "Build and publish professional portfolios in minutes, not hours. Our intuitive drag-and-drop editor and pre-designed themes make portfolio creation incredibly fast and easy.",
    keywords: ["quick portfolio", "fast portfolio builder", "instant portfolio", "rapid portfolio creation"]
  }
];

export function SEOContentSection() {
  return (
    <section className="py-24 lg:py-32 bg-backgroundSecondary relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0 bg-gradient-to-b from-backgroundPrimary via-backgroundSecondary to-backgroundPrimary"></div>
      <div className="absolute top-20 right-10 w-64 h-64 bg-gradient-to-r from-brandPrimary/4 to-brandSecondary/4 rounded-full blur-3xl"></div>
      <div className="absolute bottom-20 left-10 w-80 h-80 bg-gradient-to-r from-brandAccent/3 to-brandPrimary/3 rounded-full blur-3xl"></div>

      <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-4xl sm:text-5xl lg:text-6xl font-bold mb-6 leading-tight">
            <span className="text-textPrimary">Perfect for</span>
            <span className="gradient-text"> Every Professional</span>
          </h2>
          <p className="text-xl md:text-2xl text-textSecondary max-w-3xl mx-auto leading-relaxed">
            Whether you&apos;re a developer, designer, freelancer, or job seeker, our portfolio builder 
            has everything you need to create a stunning professional presence online.
          </p>
        </div>

        {/* Content Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-8 max-w-6xl mx-auto">
          {seoContent.map((item, index) => (
            <div
              key={index}
              className="glass-effect rounded-3xl p-8 border border-borderPrimary/50 backdrop-blur-2xl hover:border-brandPrimary/30 transition-all duration-300 hover:scale-105"
            >
              <div className="flex items-start gap-6">
                <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-brandPrimary to-brandSecondary flex items-center justify-center flex-shrink-0">
                  <item.icon className="w-6 h-6 text-white" />
                </div>
                <div className="flex-1">
                  <h3 className="text-xl font-bold text-textPrimary mb-3 leading-tight">
                    {item.title}
                  </h3>
                  <p className="text-textSecondary leading-relaxed mb-4">
                    {item.description}
                  </p>
                  <div className="flex flex-wrap gap-2">
                    {item.keywords.map((keyword, keyIndex) => (
                      <span
                        key={keyIndex}
                        className="px-3 py-1 text-xs font-medium bg-brandPrimary/10 text-brandPrimary rounded-full border border-brandPrimary/20"
                      >
                        {keyword}
                      </span>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Bottom CTA */}
        <div className="text-center mt-16">
          <div className="glass-effect rounded-3xl p-8 lg:p-10 border border-borderPrimary/50 backdrop-blur-2xl max-w-2xl mx-auto">
            <h3 className="text-2xl font-bold text-textPrimary mb-4">
              Ready to Build Your Professional Portfolio?
            </h3>
            <p className="text-textSecondary mb-6">
              Join thousands of professionals who have already created stunning portfolios with our free builder.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="bg-gradient-to-r from-brandPrimary to-brandSecondary text-white px-8 py-3 rounded-2xl font-semibold hover:shadow-lg hover:shadow-brandPrimary/25 transition-all duration-300">
                Start Building Free
              </button>
              {/* <button className="border-2 border-borderPrimary/50 hover:border-brandPrimary/50 text-textPrimary px-8 py-3 rounded-2xl font-semibold glass-effect backdrop-blur-2xl transition-all duration-300">
                View Examples
              </button> */}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}

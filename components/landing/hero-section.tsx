"use client";

import {
  <PERSON>R<PERSON>,
  Sparkles,
  Download,
  Edit3,
  Zap,
} from "lucide-react";
import { useRouter } from "next/navigation";
import { Button } from "../ui/button";

export function HeroSection() {
  const router = useRouter();

  return (
    <section
      id="home"
      className="relative min-h-screen flex items-center justify-center overflow-hidden bg-backgroundPrimary pt-24 lg:pt-32 pb-16"
    >
      {/* Clean background elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {/* Subtle accent orbs */}
        <div className="absolute top-20 right-20 w-72 h-72 rounded-full bg-brandPrimary/5 blur-3xl"></div>
        <div className="absolute bottom-20 left-20 w-80 h-80 rounded-full bg-brandAccent/4 blur-3xl"></div>

        {/* Minimal grid pattern */}
        <div
          className="absolute inset-0 opacity-[0.015]"
          style={{
            backgroundImage: `radial-gradient(circle at 1px 1px, rgba(99, 102, 241, 0.2) 1px, transparent 0)`,
            backgroundSize: "60px 60px",
          }}
        ></div>
      </div>

      <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <div className="text-center max-w-4xl mx-auto">
          {/* Animated badge */}
          <div className="inline-flex items-center space-x-2 bg-brandAccent/10 rounded-full px-4 py-2 mb-8 border border-brandAccent/20 opacity-0 animate-fade-in-up" style={{ animationDelay: '0.2s' }}>
            <Sparkles className="w-4 h-4 text-brandAccent" />
            <span className="text-sm font-medium text-brandAccent">
              Free Portfolio Builder • Export Static HTML • No Coding Required
            </span>
          </div>

          {/* Animated main heading */}
          <h1 className="text-4xl sm:text-5xl md:text-6xl lg:text-7xl font-bold tracking-tight mb-6">
            <span className="gradient-text block opacity-0 animate-fade-in-up" style={{ animationDelay: '0.4s' }}>
              Free Portfolio Builder
            </span>
            <span className="text-textPrimary block opacity-0 animate-fade-in-up" style={{ animationDelay: '0.6s' }}>
              Export Static HTML Files
            </span>
          </h1>

          {/* Animated subtitle */}
          <p className="text-lg sm:text-xl md:text-2xl text-textSecondary max-w-3xl mx-auto mb-12 leading-relaxed opacity-0 animate-fade-in-up" style={{ animationDelay: '0.8s' }}>
            Create professional portfolios with our intuitive drag-and-drop editor. Choose from stunning themes,
            export as complete static HTML websites, and showcase your work with SEO-optimized portfolios.
            Perfect for developers, designers, and freelancers.
          </p>

          {/* Clean CTA Buttons */}
          <div className="flex flex-col sm:flex-row items-center justify-center gap-4 mb-16 opacity-0 animate-fade-in-up" style={{ animationDelay: '1s' }}>
            <Button
              variant="cta"
              size="lg"
              onClick={() => router.push("/login")}
              className="px-8 py-4"
            >
              <span className="flex items-center gap-2">
                Start Building Free
                <ArrowRight className="h-5 w-5" />
              </span>
            </Button>

            <Button
              variant="outline"
              size="lg"
              onClick={() =>
                document
                  .getElementById("features")
                  ?.scrollIntoView({ behavior: "smooth" })
              }
              className="px-8 py-4"
            >
              See Features
            </Button>
          </div>

          {/* Clean key features */}
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-6 max-w-4xl mx-auto">
            <div className="text-center p-6 rounded-xl bg-backgroundSecondary/50 border border-borderPrimary hover:border-brandPrimary/30 transition-all duration-300 hover:shadow-lg opacity-0 animate-fade-in-up" style={{ animationDelay: '1.2s' }}>
              <div className="w-12 h-12 rounded-xl bg-brandPrimary flex items-center justify-center mx-auto mb-4">
                <Edit3 className="w-6 h-6 text-white" />
              </div>
              <h3 className="text-lg font-semibold text-textPrimary mb-2">WYSIWYG Editor</h3>
              <p className="text-textSecondary text-sm">No code required</p>
            </div>

            <div className="text-center p-6 rounded-xl bg-backgroundSecondary/50 border border-borderPrimary hover:border-brandAccent/30 transition-all duration-300 hover:shadow-lg opacity-0 animate-fade-in-up" style={{ animationDelay: '1.4s' }}>
              <div className="w-12 h-12 rounded-xl bg-brandAccent flex items-center justify-center mx-auto mb-4">
                <Download className="w-6 h-6 text-white" />
              </div>
              <h3 className="text-lg font-semibold text-textPrimary mb-2">Static HTML Export</h3>
              <p className="text-textSecondary text-sm">Download complete websites</p>
            </div>

            <div className="text-center p-6 rounded-xl bg-backgroundSecondary/50 border border-borderPrimary hover:border-brandSecondary/30 transition-all duration-300 hover:shadow-lg opacity-0 animate-fade-in-up" style={{ animationDelay: '1.6s' }}>
              <div className="w-12 h-12 rounded-xl bg-brandSecondary flex items-center justify-center mx-auto mb-4">
                <Zap className="w-6 h-6 text-white" />
              </div>
              <h3 className="text-lg font-semibold text-textPrimary mb-2">SEO Optimized</h3>
              <p className="text-textSecondary text-sm">Professional portfolio themes</p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}

"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { ArrowRight, Download, Palette, Sparkles } from "lucide-react";
import Image from "next/image";
import { useRouter } from "next/navigation";

const themes = [
  {
    id: "modern-theme-v1",
    name: "Modern",
    description: "Clean, professional design with dark mode styling. Perfect for developers, designers, and creative professionals who prefer darker interfaces.",
    image: "/thumbnails/Modern.png",
    category: "Dark Theme",
    features: ["Dark Mode", "Responsive Design", "SEO Optimized"],
    badgeColor: "bg-slate-600",
    accentColor: "border-slate-500"
  },
  {
    id: "creative-theme-v1",
    name: "Creative Minimalist",
    description: "Minimalist design with light, clean layouts. Ideal for professionals who prefer bright, airy interfaces with excellent readability.",
    image: "/thumbnails/Creative Minimalist.png",
    category: "Light Theme",
    features: ["Light Mode", "Responsive Design", "SEO Optimized"],
    badgeColor: "bg-blue-500",
    accentColor: "border-blue-400"
  }
];

export function ThemesSection() {
  const router = useRouter();

  return (
    <section
      id="themes"
      className="py-24 lg:py-32 bg-backgroundSecondary relative overflow-hidden"
    >
      {/* Clean Background Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-20 left-20 w-96 h-96 bg-brandPrimary/3 rounded-full blur-3xl"></div>
        <div className="absolute bottom-20 right-20 w-80 h-80 bg-brandAccent/2 rounded-full blur-3xl"></div>
      </div>

      <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative">
        {/* Enhanced Section Header */}
        <div className="text-center mb-20">
          <div className="inline-flex items-center gap-3 px-6 py-3 rounded-2xl bg-brandPrimary/10 border border-brandPrimary/20 backdrop-blur-sm mb-8">
            <Palette className="w-5 h-5 text-brandPrimary" />
            <span className="text-sm font-bold text-brandPrimary">
              Professional Portfolio Themes
            </span>
          </div>

          <h2 className="text-4xl sm:text-5xl lg:text-6xl font-bold mb-6 leading-tight">
            <span className="gradient-text">Choose Your Perfect</span>
            <br />
            <span className="text-textPrimary">Portfolio Style</span>
          </h2>

          <p className="text-lg md:text-xl text-textSecondary max-w-3xl mx-auto leading-relaxed">
            Start with our carefully crafted, mobile-responsive themes. Each design is
            professionally optimized for SEO and ready to showcase your work beautifully.
          </p>
        </div>

        {/* Dashboard-Style Themes Grid */}
        <div className="bg-backgroundPrimary rounded-2xl p-6 border border-borderPrimary shadow-sm max-w-6xl mx-auto mb-20">
          <div className="mb-6">
            <h3 className="text-xl font-semibold text-textPrimary mb-2">
              Choose Your Perfect Theme
            </h3>
            <p className="text-sm text-textSecondary">
              Select a professionally designed theme to get started. Each theme is mobile-responsive and SEO-optimized.
            </p>
          </div>

          {/* Theme Grid - Dashboard Style */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
            {themes.map((theme) => (
              <div key={theme.id} className="group">
                {/* Theme Preview Card */}
                <div className="relative w-full aspect-[5/4] bg-gradient-to-br from-backgroundSecondary to-backgroundTertiary rounded-lg overflow-hidden mb-3 shadow-sm group-hover:shadow-lg transition-all duration-300 ease-in-out">
                  <Image
                    src={theme.image}
                    alt={`${theme.name} theme preview`}
                    fill
                    className="object-contain p-4 transition-all duration-500 ease-in-out"
                  />

                  {/* Theme Category Badge */}
                  <div className="absolute top-3 right-3 z-10">
                    <div className={`${theme.badgeColor} text-white border border-white/20 rounded-full px-3 py-1 flex items-center gap-2 shadow-lg text-xs font-medium`}>
                      {theme.category}
                    </div>
                  </div>

                  {/* Hover Overlay with Action Button */}
                  <div className="absolute inset-0 bg-black/0 group-hover:bg-black/40 transition-all duration-300 ease-in-out flex items-center justify-center">
                    <Button
                      onClick={() => router.push("/login")}
                      className="opacity-0 group-hover:opacity-100 transition-all duration-300 transform scale-90 group-hover:scale-100 bg-white text-textPrimary hover:bg-backgroundSecondary shadow-lg"
                      size="sm"
                    >
                      <Download className="mr-2 h-4 w-4" />
                      Use Theme
                    </Button>
                  </div>
                </div>

                {/* Theme Info */}
                <div className="mb-4">
                  <h4 className="font-semibold text-textPrimary group-hover:text-brandPrimary transition-colors duration-300 mb-2">
                    {theme.name}
                  </h4>
                  {/* <p className="text-xs text-textSecondary leading-relaxed mb-3">
                    {theme.description}
                  </p> */}

                  {/* Features */}
                  {/* <div className="flex flex-wrap justify-center gap-1">
                    {theme.features.map((feature, featureIndex) => (
                      <div key={featureIndex} className="flex items-center gap-1 px-2 py-1 bg-backgroundSecondary rounded text-xs text-textSecondary">
                        <Check className="w-2.5 h-2.5 text-brandAccent flex-shrink-0" />
                        <span>{feature}</span>
                      </div>
                    ))}
                  </div> */}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Clean CTA Section */}
        <div className="relative">
          <div className="bg-backgroundPrimary rounded-2xl border border-borderPrimary p-8 lg:p-12 text-center shadow-lg">
            <div className="mb-6">
              <div className="w-16 h-16 bg-brandPrimary/10 rounded-2xl flex items-center justify-center mx-auto mb-4">
                <Sparkles className="w-8 h-8 text-brandPrimary" />
              </div>
              <h3 className="text-3xl md:text-4xl font-bold mb-4">
                <span className="gradient-text">More Themes Coming Soon</span>
              </h3>
            </div>

            <p className="text-textSecondary text-lg mb-8 max-w-3xl mx-auto leading-relaxed">
              We&apos;re crafting additional stunning themes including Business Professional,
              Portfolio Showcase, Creative Agency, and Developer-focused designs.
              <span className="text-brandPrimary font-semibold"> Join now to get early access!</span>
            </p>

            <div className="flex justify-center mb-8">
              <Button
                onClick={() => router.push("/login")}
                variant="cta"
                size="xl"
                className="group"
              >
                <span className="flex items-center gap-2">
                  Start Building Your Portfolio
                  <ArrowRight className="w-5 h-5 group-hover:translate-x-1 transition-transform duration-300" />
                </span>
              </Button>
            </div>

            {/* Clean Stats */}
            <div className="pt-8 border-t border-borderPrimary">
              <div className="grid grid-cols-1 sm:grid-cols-3 gap-8 text-center">
                <div className="space-y-2">
                  <div className="text-3xl font-bold text-brandPrimary">2</div>
                  <div className="text-sm text-textSecondary font-medium">Professional Themes</div>
                </div>
                <div className="space-y-2">
                  <div className="text-3xl font-bold text-brandSecondary">100%</div>
                  <div className="text-sm text-textSecondary font-medium">Mobile Responsive</div>
                </div>
                <div className="space-y-2">
                  <div className="text-3xl font-bold text-brandAccent">Free</div>
                  <div className="text-sm text-textSecondary font-medium">Forever</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}

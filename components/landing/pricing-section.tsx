"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON>, Zap, ArrowRight } from "lucide-react";
import { useRouter } from "next/navigation";

const currentFeatures = [
  "Unlimited portfolios",
  "2 beautiful themes (Modern & Creative Minimalist)",
  "Intuitive inline WYSIWYG editing",
  "Live site publishing with custom URLs",
  "Revolutionary Live DOM Capture export",
  "Complete HTML/CSS download (.zip)",
  "SEO-friendly URLs (e.g., /john-smith)",
  "Fully mobile responsive",
  "No watermarks or branding",
  "No time limits or restrictions"
];

const comingSoonFeatures = [
  "10+ premium themes",
  "Advanced customization options",
  "Team collaboration features",
  "Priority support",
  "Advanced analytics dashboard",
  "Profession specific tailored themes"
];

export function PricingSection() {
  const router = useRouter();

  return (
    <section
      id="pricing"
      className="py-24 lg:py-32 bg-backgroundSecondary relative overflow-hidden"
    >
      {/* Clean Background Elements */}
      <div className="absolute inset-0 bg-gradient-to-b from-backgroundPrimary via-backgroundSecondary to-backgroundPrimary"></div>
      <div className="absolute top-20 right-10 w-64 h-64 bg-gradient-to-r from-brandPrimary/4 to-brandSecondary/4 rounded-full blur-3xl"></div>
      <div className="absolute bottom-20 left-10 w-80 h-80 bg-gradient-to-r from-brandAccent/3 to-brandPrimary/3 rounded-full blur-3xl"></div>

      <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative">
        {/* Clean Section header */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-brandAccent/10 border border-brandAccent/20 mb-8">
            <Star className="w-4 h-4 text-brandAccent" />
            <span className="text-sm font-semibold text-brandAccent">
              Everything Free Right Now
            </span>
          </div>

          <h2 className="text-4xl sm:text-5xl lg:text-6xl font-bold mb-6 leading-tight">
            <span className="gradient-text">Start Building Today</span>
            <br />
            <span className="text-textPrimary">No Payment Required</span>
          </h2>

          <p className="text-lg md:text-xl text-textSecondary max-w-3xl mx-auto leading-relaxed mb-12">
            Get started with all the essential features you need to create a professional portfolio.
            No credit card required, no time limits, no hidden fees.
          </p>
        </div>

        {/* Current Free Features */}
        <div className="max-w-4xl mx-auto mb-16">
          <div className="bg-gradient-to-r from-brandAccent/5 to-brandPrimary/5 rounded-2xl p-8 border border-brandAccent/20">
            <div className="text-center mb-8">
              <div className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-brandAccent/20 border border-brandAccent/30 mb-4">
                <Star className="w-4 h-4 text-brandAccent" />
                <span className="text-sm font-semibold text-brandAccent">Currently Free</span>
              </div>
              <h3 className="text-3xl font-bold text-textPrimary mb-2">Everything You Need</h3>
              <p className="text-textSecondary">All features available at no cost while we&apos;re in early access</p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-8">
              {currentFeatures.map((feature, index) => (
                <div key={index} className="flex items-center gap-3">
                  <div className="w-5 h-5 rounded-full bg-brandAccent flex items-center justify-center">
                    <Check className="w-3 h-3 text-white" />
                  </div>
                  <span className="text-textSecondary">{feature}</span>
                </div>
              ))}
            </div>

            <div className="text-center">
              <Button
                variant="cta"
                size="lg"
                onClick={() => router.push("/login")}
                className="px-8 py-4"
              >
                <span className="flex items-center gap-2">
                  Start Building Free
                  <ArrowRight className="w-5 h-5" />
                </span>
              </Button>
            </div>
          </div>
        </div>

        {/* Coming Soon Features */}
        <div className="max-w-4xl mx-auto">
          <div className="bg-backgroundPrimary/50 rounded-2xl p-8 border border-brandAccent/30">
            <div className="text-center mb-8">
              <div className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-brandSecondary/10 border border-brandSecondary/20 mb-4">
                <Zap className="w-4 h-4 text-brandSecondary" />
                <span className="text-sm font-semibold text-brandSecondary">Coming Soon</span>
              </div>
              <h3 className="text-2xl font-bold text-textPrimary mb-2">Premium Features</h3>
              <p className="text-textSecondary">Advanced features we&apos;re working on for the future</p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {comingSoonFeatures.map((feature, index) => (
                <div key={index} className="flex items-center gap-3 opacity-60">
                  <div className="w-5 h-5 rounded-full bg-brandAccent flex items-center justify-center">
                    <div className="w-2 h-2 rounded-full bg-white"></div>
                  </div>
                  <span className="text-textPrimary">{feature}</span>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}


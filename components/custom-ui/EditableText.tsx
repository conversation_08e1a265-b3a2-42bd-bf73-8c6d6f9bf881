"use client";
import React, { useState, useEffect, useRef } from 'react';
import ContentEditable from 'react-contenteditable';
import { cn } from '@/lib/utils';

interface EditableTextProps {
    initialValue: string;
    isEditing: boolean;
    onSave: (value: string) => void;
    className?: string;
    tagName?: 'h1' | 'h2' | 'h3' | 'p' | 'div' | 'span';
    placeholder?: string;
    hasError?: boolean;
    errorMessage?: string;
}

const stripHtml = (html: string): string => {
    if (!html) return '';
    // This regex approach is safe for both server and client rendering.
    return html
        .replace(/<[^>]*>/g, '')      // Remove HTML tags
        .replace(/&nbsp;/g, ' ')      // Replace non-breaking spaces
        .replace(/&amp;/g, '&')       // Decode basic HTML entities
        .replace(/&lt;/g, '<')
        .replace(/&gt;/g, '>')
        .replace(/&quot;/g, '"')
        .replace(/&#39;/g, "'")
        .trim();
};

export function EditableText({
    initialValue,
    isEditing,
    onSave,
    className,
    tagName = 'div',
    placeholder,
    hasError = false,
    errorMessage
}: EditableTextProps) {
    const [text, setText] = useState(() => stripHtml(initialValue));
    const textRef = useRef(text);

    // Keep a ref to the latest text value to use in the blur handler.
    useEffect(() => {
        textRef.current = text;
    }, [text]);

    // Sync the component's state with the initialValue prop, but only when not editing.
    // This prevents the cursor from jumping and overwriting user input.
    useEffect(() => {
        const cleanInitialValue = stripHtml(initialValue);
        if (!isEditing && cleanInitialValue !== textRef.current) {
            setText(cleanInitialValue);
        }
    }, [initialValue, isEditing]);

    const handleChange = (e: { target: { value: string } }) => {
        setText(e.target.value);
    };

    const handleBlur = () => {
        const currentText = stripHtml(textRef.current);
        const originalText = stripHtml(initialValue);
        if (currentText !== originalText) {
            onSave(currentText);
        }
    };

    const handlePaste = (e: React.ClipboardEvent) => {
        e.preventDefault();
        const pastedText = e.clipboardData.getData('text/plain');
        const sanitizedText = stripHtml(pastedText);
        document.execCommand('insertText', false, sanitizedText);
    };

    if (!isEditing) {
        const Tag = tagName;
        const displayText = text || placeholder;
        // Render a non-editable tag when not in editing mode.
        // The placeholder is used as a fallback if the text is empty.
        return <Tag className={className}>{displayText}</Tag>;
    }

    return (
        <div className="relative">
            <ContentEditable
                html={text}
                disabled={false} // Always enabled when editing
                onChange={handleChange}
                onBlur={handleBlur}
                onPaste={handlePaste}
                tagName={tagName}
                className={cn(
                    'w-full transition-all duration-200 outline-none',
                    'hover:bg-muted/50 focus:bg-muted/50 rounded-md p-1',
                    hasError && 'ring-2 ring-red-200 bg-red-50/50',
                    className,
                    // Apply placeholder styles when editing and the text is empty
                    !text && 'before:content-[attr(data-placeholder)] before:text-gray-400 before:italic'
                )}
                data-placeholder={placeholder}
            />
            {hasError && errorMessage && (
                <div className="flex items-center space-x-1 my-1">
                    <span className="text-xs text-red-400">{errorMessage}</span>
                </div>
            )}
        </div>
    );
}
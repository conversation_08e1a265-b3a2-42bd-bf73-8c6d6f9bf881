"use client";
import { cn } from "@/lib/utils";
import Loader from "../ui/loader";

interface FullScreenLoaderProps {
  text?: string;
  className?: string;
}

export function FullScreenLoader({
  text,
  className
}: FullScreenLoaderProps) {
  return (
    <div
      className={cn(
        "fixed inset-0 bg-background z-50 flex items-center justify-center",
        className
      )}
    >
      <Loader text={text} />
    </div>
  );
}

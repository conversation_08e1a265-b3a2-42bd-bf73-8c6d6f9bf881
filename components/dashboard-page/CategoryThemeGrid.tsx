"use client";

import { useState } from "react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useRouter } from "next/navigation";
import Image from "next/image";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Loader2, Check, Edit, Palette, Stethoscope, Code, Camera, Building } from "lucide-react";
import { toast } from "sonner";
import {
  getThemesByThemeCategory,
  getThemesByProfession,
  getAllThemeCategories,
  getAllProfessionTypes,
  ThemeConfig
} from "@/themes/theme-registry";
import { updatePortfolioTheme, createPortfolioFromTemplateWithProfession } from "@/lib/portfolio-api";
import { User, ThemeCategory, ProfessionType } from "@/lib/types";

interface Portfolio {
  id: string;
  templateId: string;
  isPublished: boolean;
  slug: string;
  themeCategory?: ThemeCategory;
  professionType?: ProfessionType;
}

interface CategoryThemeGridProps {
  user: User | null;
  portfolio: Portfolio | null;
}

const categoryIcons = {
  general: Palette,
  healthcare: Stethoscope,
  technology: Code,
  creative: Camera,
  business: Building,
  education: Building,
  organization: Building,
};

const professionLabels: Record<ProfessionType, string> = {
  general: 'General',
  doctor: 'Doctor',
  nurse: 'Nurse',
  'it-professional': 'IT Professional',
  photographer: 'Photographer',
  designer: 'Designer',
  teacher: 'Teacher',
  'business-analyst': 'Business Analyst',
  traveller: 'Travel Blogger',
};

export default function CategoryThemeGrid({ user, portfolio }: CategoryThemeGridProps) {
  const [loadingThemeId, setLoadingThemeId] = useState<string | null>(null);
  const [selectedCategory, setSelectedCategory] = useState<ThemeCategory>('general');
  const [selectedProfession, setSelectedProfession] = useState<ProfessionType>('general');
  
  const queryClient = useQueryClient();
  const router = useRouter();

  // For existing portfolio - theme switching
  const switchThemeMutation = useMutation({
    mutationFn: async ({ portfolioId, templateId }: { portfolioId: string; templateId: string }) => {
      return updatePortfolioTheme(portfolioId, templateId);
    },
    onMutate: (variables) => {
      setLoadingThemeId(variables.templateId);
    },
    onSuccess: async () => {
      await Promise.all([
        queryClient.invalidateQueries({ queryKey: ['portfolios', user?.uid] }),
        queryClient.refetchQueries({ queryKey: ['portfolios', user?.uid] })
      ]);
      toast.success("Theme applied successfully!");
      setLoadingThemeId(null);
    },
    onError: (error: Error) => {
      toast.error(`Failed to switch theme: ${error.message}`);
      setLoadingThemeId(null);
    }
  });

  // For new portfolio creation
  const createPortfolioMutation = useMutation({
    mutationFn: async ({ user, templateId, professionType }: {
      user: User;
      templateId: string;
      professionType: ProfessionType;
    }) => {
      return createPortfolioFromTemplateWithProfession(user, templateId, professionType);
    },
    onMutate: (variables) => {
      setLoadingThemeId(variables.templateId);
      toast.loading('Creating your portfolio...', { id: 'portfolio-creation' });
    },
    onSuccess: async () => {
      toast.dismiss('portfolio-creation');
      toast.success("Portfolio created! Redirecting to editor...");
      setLoadingThemeId(null);
      router.push('/portfolio');
    },
    onError: (error: Error) => {
      toast.dismiss('portfolio-creation');
      toast.error(`Failed to create portfolio: ${error.message}`);
      setLoadingThemeId(null);
      router.push('/dashboard');
    }
  });

  const handleThemeAction = (themeId: string, professionType: ProfessionType = 'general') => {
    if (!user) {
      toast.error("You must be logged in to select a theme.");
      return;
    }

    if (portfolio) {
      // Switch theme for existing portfolio
      switchThemeMutation.mutate({ portfolioId: portfolio.id, templateId: themeId });
    } else {
      // Create new portfolio with selected theme and profession type
      createPortfolioMutation.mutate({ user, templateId: themeId, professionType });
    }
  };

  const renderThemeCard = (theme: ThemeConfig) => {
    const isCurrentTheme = portfolio?.templateId === theme.id;
    const isLoading = loadingThemeId === theme.id;

    return (
      <Card key={theme.id} className="group relative overflow-hidden transition-all duration-300 hover:shadow-lg">
        <div className="relative aspect-video overflow-hidden">
          {theme.preview && (
            <Image
              src={theme.preview}
              alt={`${theme.name} theme preview`}
              fill
              className="object-cover transition-transform duration-300 group-hover:scale-105"
            />
          )}
          
          {/* Overlay with action buttons */}
          <div className="absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
            <div className="flex gap-2">
              {portfolio ? (
                <Button
                  onClick={() => handleThemeAction(theme.id)}
                  disabled={isLoading || isCurrentTheme}
                  size="sm"
                  variant={isCurrentTheme ? "secondary" : "default"}
                >
                  {isLoading ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : isCurrentTheme ? (
                    <>
                      <Check className="h-4 w-4 mr-1" />
                      Current
                    </>
                  ) : (
                    <>
                      <Edit className="h-4 w-4 mr-1" />
                      Switch
                    </>
                  )}
                </Button>
              ) : (
                <Button
                  onClick={() => handleThemeAction(theme.id, selectedProfession)}
                  disabled={isLoading}
                  size="sm"
                >
                  {isLoading ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : (
                    <>
                      <Edit className="h-4 w-4 mr-1" />
                      Create
                    </>
                  )}
                </Button>
              )}
            </div>
          </div>
        </div>

        <CardContent className="p-4">
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <h3 className="font-semibold text-lg mb-1">{theme.name}</h3>
              <p className="text-sm text-muted-foreground mb-3">{theme.description}</p>
              
              <div className="flex flex-wrap gap-1 mb-2">
                {theme.tags?.map((tag) => (
                  <Badge key={tag} variant="secondary" className="text-xs">
                    {tag}
                  </Badge>
                ))}
                {theme.isPremium && (
                  <Badge variant="default" className="text-xs bg-gradient-to-r from-purple-500 to-pink-500">
                    Premium
                  </Badge>
                )}
              </div>
              
              <div className="text-xs text-muted-foreground">
                v{theme.version} • {theme.author}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  };

  const categories = getAllThemeCategories();
  const professionTypes = getAllProfessionTypes();
  
  // Get themes based on selected filters
  const getFilteredThemes = () => {
    if (selectedCategory === 'general') {
      return getThemesByProfession(selectedProfession);
    }
    return getThemesByThemeCategory(selectedCategory);
  };

  const filteredThemes = getFilteredThemes();

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-bold mb-2">
          {portfolio ? 'Switch Theme' : 'Choose Your Theme'}
        </h2>
        <p className="text-muted-foreground">
          {portfolio 
            ? 'Select a new theme for your portfolio' 
            : 'Pick a theme that matches your profession and style'
          }
        </p>
      </div>

      <Tabs value={selectedCategory} onValueChange={(value) => setSelectedCategory(value as ThemeCategory)}>
        <TabsList className="grid w-full grid-cols-4 lg:grid-cols-7">
          {categories.map((category) => {
            const Icon = categoryIcons[category] || Palette;
            return (
              <TabsTrigger key={category} value={category} className="flex items-center gap-1">
                <Icon className="h-4 w-4" />
                <span className="hidden sm:inline capitalize">{category}</span>
              </TabsTrigger>
            );
          })}
        </TabsList>

        {/* Profession selector for general category */}
        {selectedCategory === 'general' && (
          <div className="mt-4">
            <label className="text-sm font-medium mb-2 block">Select your profession:</label>
            <div className="flex flex-wrap gap-2">
              {professionTypes.map((profession) => (
                <Button
                  key={profession}
                  variant={selectedProfession === profession ? "default" : "outline"}
                  size="sm"
                  onClick={() => setSelectedProfession(profession)}
                >
                  {professionLabels[profession]}
                </Button>
              ))}
            </div>
          </div>
        )}

        <TabsContent value={selectedCategory} className="mt-6">
          {filteredThemes.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredThemes.map(renderThemeCard)}
            </div>
          ) : (
            <div className="text-center py-12">
              <Palette className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
              <h3 className="text-lg font-semibold mb-2">No themes available</h3>
              <p className="text-muted-foreground">
                Themes for this category are coming soon!
              </p>
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}

"use client";

import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Check, Sparkles } from "lucide-react";

interface User {
  uid: string;
  displayName: string | null;
  email: string | null;
  photoURL: string | null;
}

interface WelcomeSectionProps {
  user: User | null;
  hasPortfolio: boolean;
}

export default function WelcomeSection({ user, hasPortfolio }: WelcomeSectionProps) {
  if (hasPortfolio) {
    // Existing user with portfolio - simple welcome
    return (
      <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-2xl p-8 border border-blue-100">
        <div className="flex items-center space-x-4 mb-4">
          <Avatar className="h-16 w-16 border-2 hover:border-brandPrimary transition-colors">
            <AvatarImage src={user?.photoURL ?? undefined} alt={user?.displayName ?? ""} />
            <AvatarFallback>{user?.displayName?.charAt(0).toUpperCase()}</AvatarFallback>
          </Avatar>
          <div>
            <h2 className="text-3xl font-bold text-slate-800">
              Welcome back, {user?.displayName}!
            </h2>
            <p className="text-slate-600 text-lg">
              Ready to manage your portfolio and showcase your work to the world.
            </p>
          </div>
        </div>
      </div>
    );
  }

  // New user without portfolio - enhanced welcome
  return (
    <div className="relative bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 rounded-3xl p-8 lg:p-12 border border-blue-100/50 text-center overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute top-10 left-10 w-20 h-20 bg-brandPrimary rounded-full blur-xl"></div>
        <div className="absolute bottom-10 right-10 w-32 h-32 bg-brandSecondary rounded-full blur-xl"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-40 h-40 bg-purple-400 rounded-full blur-2xl"></div>
      </div>

      <div className="relative z-10">
        <div className="flex items-center justify-center mb-6">
          <div className="relative">
            <Avatar className="h-20 w-20 border-4 border-white shadow-xl ring-4 ring-brandPrimary/20">
              <AvatarImage src={user?.photoURL ?? undefined} alt={user?.displayName ?? ""} />
              <AvatarFallback className="text-xl font-bold bg-gradient-to-br from-brandPrimary to-brandSecondary text-white">
                {user?.displayName?.charAt(0).toUpperCase()}
              </AvatarFallback>
            </Avatar>
            <div className="absolute -bottom-2 -right-2 bg-green-500 w-6 h-6 rounded-full border-2 border-white flex items-center justify-center">
              <Check className="w-3 h-3 text-white" />
            </div>
          </div>
        </div>

        <h2 className="text-4xl lg:text-5xl font-bold bg-gradient-to-r from-gray-900 via-gray-800 to-gray-900 bg-clip-text text-transparent mb-4">
          Welcome to Profolify, {user?.displayName?.split(' ')[0]}!
        </h2>

        <p className="text-xl lg:text-2xl text-gray-600 mb-8 max-w-2xl mx-auto leading-relaxed">
          Let&#39;s create your professional portfolio in minutes
        </p>

        <div className="inline-flex items-center space-x-3 bg-white/80 backdrop-blur-sm rounded-full px-6 py-3 text-sm font-medium text-gray-700 shadow-lg border border-white/50">
          <div className="w-8 h-8 bg-gradient-to-r from-brandPrimary to-brandSecondary rounded-full flex items-center justify-center">
            <Sparkles className="w-4 h-4 text-white" />
          </div>
          <span>Choose a template below to get started</span>
        </div>
      </div>
    </div>
  );
}

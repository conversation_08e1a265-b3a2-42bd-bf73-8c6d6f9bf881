"use client";

import Link from "next/link";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu";
import { Loader2, LogOut, Settings } from "lucide-react";

interface User {
  uid: string;
  displayName: string | null;
  email: string | null;
  photoURL: string | null;
}

interface DashboardHeaderProps {
  user: User | null;
  onSignOut: () => Promise<void>;
  isSigningOut: boolean;
  title?: string;
  subtitle?: string;
}

export default function DashboardHeader({ user, onSignOut, isSigningOut, title, subtitle }: DashboardHeaderProps) {
  const router = useRouter();

  return (
    <nav className="border-b bg-white/95 backdrop-blur-sm sticky top-0 z-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
        <div className="flex items-center justify-between">
          {/* Logo and Title Section */}
          <div className="flex items-center space-x-4">
            <Link
              href="/"
              className="flex items-center gap-2 relative z-10"
            >
              <Image src="/icon.png" alt="logo" width={32} height={32} className="flex-shrink-0" />
              <span className="font-bold text-xl lg:text-2xl gradient-text hidden sm:block">
                Profolify
              </span>
            </Link>

            {/* Optional Title and Subtitle */}
            {title && (
              <div className="hidden md:flex flex-col items-start border-l border-gray-200 pl-4">
                <h1 className="text-sm font-bold text-slate-800">{title}</h1>
                {subtitle && <p className="text-xs text-slate-500">{subtitle}</p>}
              </div>
            )}
          </div>

          {/* User Dropdown */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="relative h-10 w-10 sm:h-12 sm:w-12 rounded-full p-0 hover:bg-transparent">
                <Avatar className="h-10 w-10 sm:h-12 sm:w-12 border-2 hover:border-brandPrimary transition-colors">
                  <AvatarImage src={user?.photoURL ?? undefined} alt={user?.displayName ?? ""} />
                  <AvatarFallback>{user?.displayName?.charAt(0).toUpperCase()}</AvatarFallback>
                </Avatar>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="w-56" align="end" forceMount>
              <div className="flex items-center justify-start gap-2 p-2">
                <div className="flex flex-col space-y-1 leading-none">
                  <p className="font-medium">{user?.displayName}</p>
                  <p className="w-[200px] truncate text-sm text-muted-foreground">
                    {user?.email}
                  </p>
                </div>
              </div>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={() => router.push('/settings')}>
                <Settings className="mr-2 h-4 w-4 hover:text-white" />
                <span>Settings</span>
              </DropdownMenuItem>
              <DropdownMenuItem onClick={onSignOut} disabled={isSigningOut}>
                {isSigningOut ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : <LogOut className="mr-2 h-4 w-4 hover:text-white" />}
                <span>{isSigningOut ? 'Signing out...' : 'Sign out'}</span>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </nav>
  );
}

"use client";

import { useState } from "react";
import Link from "next/link";
import Image from "next/image";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import {
  Loader2,
  ExternalLink,
  Trash2,
  Copy,
  Check,
  Download,
  Edit,
} from "lucide-react";
import { getThemeById } from "@/themes/theme-registry";
import { toast } from "sonner";
import {
  TwitterShareButton,
  FacebookShareButton,
  LinkedinShareButton,
  TwitterIcon,
  FacebookIcon,
  LinkedinIcon,
} from "react-share";

interface Portfolio {
  id: string;
  templateId: string;
  isPublished: boolean;
  slug: string;
  userName: string;
}

interface CurrentPortfolioCardProps {
  portfolio: Portfolio;
  onExport?: () => void;
  isExporting?: boolean;
  onDelete?: () => void;
}

export default function CurrentPortfolioCard({
  portfolio,
  onExport,
  isExporting = false,
  onDelete
}: CurrentPortfolioCardProps) {
  const [copied, setCopied] = useState(false);
  const currentTheme = getThemeById(portfolio.templateId);

  // Generate portfolio URL
  const fullUrl = `${typeof window !== "undefined" ? window.location.origin : "https://profolify.com"}/${portfolio.slug}`;
  const shortUrl = `profolify.com/${portfolio.slug}`;

  const handleCopyLink = async () => {
    try {
      await navigator.clipboard.writeText(fullUrl);
      setCopied(true);
      toast.success("Link copied to clipboard!");
      setTimeout(() => setCopied(false), 2000);
    } catch {
      toast.error("Failed to copy link. Please try again.");
    }
  };

  const shareTitle = `Check out ${portfolio.userName}'s portfolio!`;
  const shareDescription = `Discover ${portfolio.userName}'s amazing work and projects.`;

  return (
    <div className="bg-white rounded-2xl p-6 border border-gray-200">
      {/* Status Badge */}
      <Badge
        variant={portfolio.isPublished ? "default" : "secondary"}
        className={`w-fit mb-2 py-1 flex items-center gap-2 rounded-full px-2 ${portfolio.isPublished
          ? "bg-brandAccent text-white border-brandAccent"
          : "bg-yellow-100 text-yellow-800 border-yellow-200"
          }`}
      >
        {portfolio.isPublished ? "Published" : "Draft"}
      </Badge>

      {/* Title */}
      <h3 className="text-2xl font-semibold text-gray-900 mb-2">Current Portfolio</h3>

      {/* Theme Description */}
      <p className="text-sm text-gray-600 mb-6">
        Theme: {currentTheme?.name || "Unknown"} - {currentTheme?.description || "A clean and modern theme for showcasing your work."}
      </p>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Left Column - Portfolio Preview */}
        <div className="relative group">
          <div className="w-full aspect-[16/9] bg-gray-400 overflow-hidden relative">
            {currentTheme?.preview ? (
              <Image
                src={currentTheme.preview}
                alt={`${currentTheme.name} preview`}
                fill
                className="object-contain p-8"
              />
            ) : (
              <div className="bg-gray-400 h-full flex items-center justify-center">
                <div className="text-center text-white">
                  <div className="w-16 h-16 bg-white/20 rounded-full mx-auto mb-2"></div>
                  <div className="text-sm font-medium">Portfolio Preview</div>
                </div>
              </div>
            )}

            {/* Hover Overlay with Edit Button */}
            <div className="absolute inset-0 bg-black/0 group-hover:bg-black/40 transition-all duration-300 ease-in-out flex items-center justify-center">
              <Button
                asChild
                className="opacity-0 group-hover:opacity-100 transition-all duration-300 transform scale-90 group-hover:scale-100 bg-brandAccent text-white hover:bg-brandAccent/90 shadow-lg rounded-xs"
              >
                <Link href="/portfolio">
                  <Edit className="mr-2 h-4 w-4" />
                  Edit Portfolio
                </Link>
              </Button>
            </div>
          </div>
        </div>

        {/* Right Column - Actions & Sharing */}
        <div className="space-y-4">
          {/* Action Buttons */}
          <div className="flex flex-wrap gap-5">
            <Button
              onClick={onDelete}
              variant="destructive"
              size="sm"
              className="bg-red-500 max-w-[200px] text-white hover:bg-red-600 hover:border-red-600 rounded-xs  shadow-none hover:scale-none hover:shadow-none"
            >
              <Trash2 className="mr-2 h-4 w-4" />
              Delete
            </Button>

            <Button
              onClick={onExport}
              size="sm"
              disabled={isExporting || !portfolio.isPublished}
              className="bg-brandAccent max-w-[200px] hover:bg-brandAccent/80 text-white rounded-xs border-brandAccent hover:border-brandAccent/80 shadow-none hover:scale-none hover:shadow-none"
            >
              {isExporting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Exporting...
                </>
              ) : (
                <>
                  <Download className="mr-2 h-4 w-4" />
                  Export Site
                </>
              )}
            </Button>
            {/* View Live Button (if published) */}
            {portfolio.isPublished && (
              <Button asChild variant="outline" size="sm" className="w-full max-w-[200px] rounded-xs  shadow-none hover:scale-none hover:shadow-none">
                <Link href={`/${portfolio.slug}`} target="_blank">
                  <ExternalLink className="mr-2 h-4 w-4" />
                  View Live
                </Link>
              </Button>
            )}
          </div>



          {/* Shareable Links Section (if published) */}
          {portfolio.isPublished ? (
            <div className="space-y-4 pt-4">
              <h4 className="text-lg font-semibold text-gray-900">Share Your Portfolio</h4>

              {/* Copy Link */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700">Portfolio Link</label>
                <div className="flex items-center gap-2">
                  <div className="flex-1 relative">
                    <Input readOnly value={shortUrl} className="pr-20 font-mono text-sm focus-visible:ring-0" />
                    <div className="absolute right-2 top-1/2 -translate-y-1/2 flex items-center gap-1">
                      <Button size="sm" variant="ghost" onClick={handleCopyLink} className="h-7 px-2 hover:text-brandAccent">
                        {copied ? <Check className="h-3 w-3 text-green-600" /> : <Copy className="h-3 w-3" />}
                      </Button>
                    </div>
                  </div>
                </div>
              </div>

              {/* Social Media Share Buttons */}
              <div className="space-y-3">
                <label className="text-sm font-medium text-gray-700 mb-2">Share Portfolio</label>
                <div className="flex items-center gap-4 justify-start mt-2 flex-wrap">
                  <TwitterShareButton
                    url={fullUrl}
                    title={shareTitle}
                    hashtags={['portfolio', 'webdev']}
                    className="group transition-all duration-200 hover:scale-105 hover:shadow-md rounded-full"
                  >
                    <TwitterIcon size={36} round className="hover:opacity-80 transition-opacity" />
                  </TwitterShareButton>

                  <FacebookShareButton
                    url={fullUrl}
                    hashtag="#portfolio"
                    className="group transition-all duration-200 hover:scale-105 hover:shadow-md rounded-full"
                  >
                    <FacebookIcon size={36} round className="hover:opacity-80 transition-opacity" />
                  </FacebookShareButton>

                  <LinkedinShareButton
                    url={fullUrl}
                    title={shareTitle}
                    summary={shareDescription}
                    source="Profolify"
                    className="group transition-all duration-200 hover:scale-105 hover:shadow-md rounded-full"
                  >
                    <LinkedinIcon size={36} round className="hover:opacity-80 transition-opacity" />
                  </LinkedinShareButton>
                </div>
              </div>
            </div>
          ) : (
            <div className="space-y-4 pt-4">
              {/* Clean Status Message */}
              <div className="space-y-3">
                <p className="text-sm text-gray-600 max-w-md leading-relaxed">
                  Your portfolio is not published yet. Edit and publish your portfolio to share it with the world.
                </p>
              </div>

              {/* Action Buttons */}
              <div className="space-y-3">
                <Button
                  asChild
                  size="sm"
                  className="w-full max-w-[200px] bg-brandAccent text-white hover:bg-brandAccent/90 shadow-lg rounded-xs"
                >
                  <Link href="/portfolio">
                    <Edit className="mr-2 h-4 w-4" />
                    Edit Portfolio
                  </Link>
                </Button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

"use client";

import { useState } from "react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useRouter } from "next/navigation";
import Image from "next/image";
import { Button } from "@/components/ui/button";
import { Loader2, Check, Edit } from "lucide-react";
import { toast } from "sonner";
import { getAllThemes } from "@/themes/theme-registry";
import { updatePortfolioTheme, createPortfolioFromTemplate } from "@/lib/portfolio-api";
import { User } from "@/lib/types";

interface Portfolio {
  id: string;
  templateId: string;
  isPublished: boolean;
  slug: string;
}

interface ThemeGridProps {
  user: User | null;
  portfolio: Portfolio | null;
}

export default function ThemeGrid({ user, portfolio }: ThemeGridProps) {
  const router = useRouter();
  const queryClient = useQueryClient();
  const [loadingThemeId, setLoadingThemeId] = useState<string | null>(null);

  // For existing portfolio - theme switching
  const switchThemeMutation = useMutation({
    mutationFn: async ({ portfolioId, templateId }: { portfolioId: string; templateId: string }) => {
      return updatePortfolioTheme(portfolioId, templateId);
    },
    onMutate: (variables) => {
      setLoadingThemeId(variables.templateId);
    },
    onSuccess: async () => {
      // Batch the query updates to reduce re-renders
      await Promise.all([
        queryClient.invalidateQueries({ queryKey: ['portfolios', user?.uid] }),
        queryClient.refetchQueries({ queryKey: ['portfolios', user?.uid] })
      ]);

      // Show success message
      toast.success("Theme applied successfully!");
      setLoadingThemeId(null);
    },
    onError: (error: Error) => {
      toast.error(`Failed to switch theme: ${error.message}`);
      setLoadingThemeId(null);
    }
  });

  // For new portfolio - template selection
  const createPortfolioMutation = useMutation({
    mutationFn: createPortfolioFromTemplate,
    onMutate: (variables) => {
      setLoadingThemeId(variables.templateId);

      // Navigate immediately when creation starts
      router.push('/portfolio?creating=true');
      toast.loading("Creating your portfolio...", { id: 'portfolio-creation' });
    },
    onSuccess: (newPortfolio) => {
      // Update cache after successful creation
      queryClient.setQueryData(['portfolios', user?.uid], [newPortfolio]);

      // Dismiss loading toast and show success
      toast.dismiss('portfolio-creation');
      toast.success("Portfolio created successfully!");

      // Clear loading state
      setLoadingThemeId(null);
    },
    onError: (error: Error) => {
      // Dismiss loading toast and show error
      toast.dismiss('portfolio-creation');
      toast.error(`Failed to create portfolio: ${error.message}`);
      setLoadingThemeId(null);

      // Navigate back to dashboard on error
      router.push('/dashboard');
    }
  });

  const handleThemeAction = (themeId: string) => {
    if (!user) {
      toast.error("You must be logged in to select a theme.");
      return;
    }

    if (portfolio) {
      // Switch theme for existing portfolio
      switchThemeMutation.mutate({ portfolioId: portfolio.id, templateId: themeId });
    } else {
      // Create new portfolio with selected theme
      createPortfolioMutation.mutate({ user, templateId: themeId });
    }
  };

  const themes = getAllThemes();

  return (
    <div className="bg-white rounded-2xl p-4 sm:p-6 border border-gray-200">
      <div className="mb-6">
        <h3 className="text-lg sm:text-xl font-semibold text-gray-900 mb-2">
          {portfolio ? "Switch Theme" : "Choose Your Theme"}
        </h3>
        <p className="text-sm text-gray-600">
          {portfolio
            ? "You can switch between themes anytime. Your content will be preserved when switching."
            : "Select a theme to get started with your portfolio. You can change it later anytime."
          }
        </p>
      </div>

      {/* Horizontal theme grid */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
        {themes.map((theme) => {
          const isCurrentTheme = portfolio?.templateId === theme.id;
          const isLoadingThisTheme = loadingThemeId === theme.id;

          return (
            <div key={theme.id} className="group">
              {/* Theme preview */}
              <div className="relative w-full aspect-[5/4] bg-gradient-to-br from-gray-100 to-gray-200 rounded-sm overflow-hidden mb-3 shadow-sm group-hover:shadow-lg transition-all duration-300 ease-in-out">
                <Image
                  src={theme.preview || '/thumbnails/default-theme.jpg'}
                  alt={`${theme.name} preview`}
                  fill
                  className="object-contain p-8 transition-all duration-500 ease-in-out"
                />

                {/* Current theme indicator - always visible */}
                {isCurrentTheme && (
                  <div className="absolute top-3 right-3 z-10">
                    <div className="bg-green-100 text-green-800 border border-green-200 rounded-full px-3 py-1 flex items-center gap-2 shadow-lg text-xs font-medium">
                      <Check className="w-3 h-3" />
                      Current
                    </div>

                  </div>
                )}

                {/* Hover overlay with action button */}
                <div className="absolute inset-0 bg-black/0 group-hover:bg-black/40 transition-all duration-300 ease-in-out flex items-center justify-center">
                  {!isCurrentTheme ? (
                    <Button
                      onClick={() => handleThemeAction(theme.id)}
                      disabled={isLoadingThisTheme}
                      className="opacity-0 group-hover:opacity-100 transition-all duration-300 transform scale-90 group-hover:scale-100 bg-white text-gray-900 hover:bg-gray-100 shadow-lg rounded-xs"
                    >
                      {isLoadingThisTheme ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          {portfolio ? "Applying..." : "Creating..."}
                        </>
                      ) : (
                        portfolio ? "Apply Theme" : "Use Theme"
                      )}
                    </Button>
                  ) : (
                    /* Edit button for current theme */
                    <Button
                      onClick={() => router.push('/portfolio')}
                      className="opacity-0 group-hover:opacity-100 transition-all duration-300 transform scale-90 group-hover:scale-100 bg-brandAccent text-white hover:bg-brandAccent/90 shadow-lg rounded-xs"
                    >
                      <Edit className="mr-2 h-4 w-4" />
                      Edit Portfolio
                    </Button>
                  )}
                </div>

                {/* Loading overlay */}
                {isLoadingThisTheme && (
                  <div className="absolute inset-0 bg-white/90 backdrop-blur-sm flex items-center justify-center z-20">
                    <Loader2 className="w-6 h-6 animate-spin text-brandPrimary" />
                  </div>
                )}
              </div>

              {/* Theme info */}
              <div className="text-center">
                <h4 className="font-medium text-gray-900 group-hover:text-brandPrimary transition-colors duration-300">{theme.name}</h4>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
}

"use client";
import React from 'react';
import { AlertCircle, AlertTriangle, X, ExternalLink } from 'lucide-react';
import { ValidationError } from '@/lib/portfolio-validation';
import { cn } from '@/lib/utils';

interface ValidationErrorDisplayProps {
  errors: ValidationError[];
  warnings?: ValidationError[];
  className?: string;
  showWarnings?: boolean;
  onDismiss?: () => void;
}

// Group errors by section
const groupErrorsBySection = (errors: ValidationError[]) => {
  const grouped: Record<string, ValidationError[]> = {};

  errors.forEach(error => {
    const section = error.section || 'General';
    if (!grouped[section]) {
      grouped[section] = [];
    }
    grouped[section].push(error);
  });

  return grouped;
};

export function ValidationErrorDisplay({
  errors,
  warnings = [],
  className,
  showWarnings = true,
  onDismiss
}: ValidationErrorDisplayProps) {
  if (errors.length === 0 && (!showWarnings || warnings.length === 0)) {
    return null;
  }

  const groupedErrors = groupErrorsBySection(errors);
  const groupedWarnings = groupErrorsBySection(warnings);

  return (
    <div className={cn("space-y-6", className)}>
      {/* Errors */}
      {errors.length > 0 && (
        <div className="bg-red-50 border border-red-200 rounded-xl p-6">
          <div className="flex items-start justify-between mb-4">
            <div className="flex items-center space-x-3">
              <div className="flex items-center justify-center w-8 h-8 bg-red-100 rounded-full">
                <AlertCircle className="h-4 w-4 text-red-600" />
              </div>
              <div>
                <h3 className="text-base font-semibold text-red-800">
                  Required Fields Missing
                </h3>
                <p className="text-sm text-red-600 mt-1">
                  Please complete these fields to publish your portfolio
                </p>
              </div>
            </div>
            {onDismiss && (
              <button
                onClick={onDismiss}
                className="text-red-400 hover:text-red-500 transition-colors p-1"
              >
                <X className="h-4 w-4" />
              </button>
            )}
          </div>

          <div className="space-y-4">
            {Object.entries(groupedErrors).map(([section, sectionErrors]) => (
              <div key={section} className="bg-white/60 rounded-lg p-4 border border-red-100">
                <h4 className="text-sm font-medium text-red-800 mb-3 flex items-center">
                  <span className="w-2 h-2 bg-red-400 rounded-full mr-2"></span>
                  {section}
                </h4>
                <div className="space-y-2">
                  {sectionErrors.map((error, index) => (
                    <div key={index} className="flex items-start space-x-2">
                      <div className="w-1.5 h-1.5 bg-red-400 rounded-full mt-2 flex-shrink-0"></div>
                      <span className="text-sm text-red-700 leading-relaxed">
                        {error.message}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>

          <div className="mt-4 p-3 bg-red-100/50 rounded-lg border border-red-200">
            <p className="text-xs text-red-700 flex items-center">
              <ExternalLink className="h-3 w-3 mr-1" />
              Tip: Look for fields with dotted borders in your portfolio editor - these indicate required fields.
            </p>
          </div>
        </div>
      )}

      {/* Warnings */}
      {showWarnings && warnings.length > 0 && (
        <div className="bg-amber-50 border border-amber-200 rounded-xl p-6">
          <div className="flex items-start justify-between mb-4">
            <div className="flex items-center space-x-3">
              <div className="flex items-center justify-center w-8 h-8 bg-amber-100 rounded-full">
                <AlertTriangle className="h-4 w-4 text-amber-600" />
              </div>
              <div>
                <h3 className="text-base font-semibold text-amber-800">
                  Recommendations
                </h3>
                <p className="text-sm text-amber-600 mt-1">
                  Optional improvements to make your portfolio stand out
                </p>
              </div>
            </div>
            {onDismiss && (
              <button
                onClick={onDismiss}
                className="text-amber-400 hover:text-amber-600 transition-colors p-1"
              >
                <X className="h-4 w-4" />
              </button>
            )}
          </div>

          <div className="space-y-4">
            {Object.entries(groupedWarnings).map(([section, sectionWarnings]) => (
              <div key={section} className="bg-white/60 rounded-lg p-4 border border-amber-100">
                <h4 className="text-sm font-medium text-amber-800 mb-3 flex items-center">
                  <span className="w-2 h-2 bg-amber-400 rounded-full mr-2"></span>
                  {section}
                </h4>
                <div className="space-y-2">
                  {sectionWarnings.map((warning, index) => (
                    <div key={index} className="flex items-start space-x-2">
                      <div className="w-1.5 h-1.5 bg-amber-400 rounded-full mt-2 flex-shrink-0"></div>
                      <span className="text-sm text-amber-700 leading-relaxed">
                        {warning.message}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}

// Inline field error component
interface FieldErrorProps {
  error?: string;
  className?: string;
}

export function FieldError({ error, className }: FieldErrorProps) {
  if (!error) return null;

  return (
    <div className={cn("flex items-center space-x-1 mt-1", className)}>
      <AlertCircle className="h-3 w-3 text-red-400 flex-shrink-0" />
      <span className="text-xs text-red-500">{error}</span>
    </div>
  );
}

// Field wrapper with validation styling
interface ValidatedFieldProps {
  children: React.ReactNode;
  error?: string;
  warning?: string;
  className?: string;
}

export function ValidatedField({ children, error, warning, className }: ValidatedFieldProps) {
  return (
    <div className={cn("relative", className)}>
      <div className={cn(
        "transition-all duration-200",
        error && "ring-2 ring-red-200 rounded-md",
        warning && !error && "ring-2 ring-yellow-200 rounded-md"
      )}>
        {children}
      </div>
      {error && <FieldError error={error} />}
      {warning && !error && (
        <div className="flex items-center space-x-1 mt-1">
          <AlertTriangle className="h-3 w-3 text-yellow-500 flex-shrink-0" />
          <span className="text-xs text-yellow-600">{warning}</span>
        </div>
      )}
    </div>
  );
}

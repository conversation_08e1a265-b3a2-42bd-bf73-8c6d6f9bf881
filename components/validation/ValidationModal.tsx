"use client";
import React from 'react';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { ValidationErrorDisplay } from './ValidationErrorDisplay';
import { ValidationResult } from '@/lib/portfolio-validation';
import { AlertCircle, CheckCircle, X } from 'lucide-react';

interface ValidationModalProps {
  isOpen: boolean;
  onClose: () => void;
  validationResult: ValidationResult;
  onProceedAnyway?: () => void;
  allowProceedWithWarnings?: boolean;
}

export function ValidationModal({
  isOpen,
  onClose,
  validationResult,
  onProceedAnyway,
  allowProceedWithWarnings = false
}: ValidationModalProps) {
  const hasErrors = validationResult.errors.length > 0;
  const hasWarnings = validationResult.warnings.length > 0;
  const hasOnlyWarnings = !hasErrors && hasWarnings;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-3xl max-h-[85vh] overflow-hidden p-0" showCloseButton={false}>
        {/* Header */}
        <div className="px-6 py-4 border-b bg-gray-50/50">
          <div className="flex items-center justify-between">
            <DialogHeader className="space-y-1">
              <DialogTitle className="flex items-center space-x-3 text-lg font-semibold">
                {hasErrors ? (
                  <>
                    <div className="flex items-center justify-center w-8 h-8 bg-red-100 rounded-full">
                      <AlertCircle className="h-4 w-4 text-red-600" />
                    </div>
                    <span className="text-gray-900">Portfolio Validation Issues</span>
                  </>
                ) : hasOnlyWarnings ? (
                  <>
                    <div className="flex items-center justify-center w-8 h-8 bg-yellow-100 rounded-full">
                      <CheckCircle className="h-4 w-4 text-yellow-600" />
                    </div>
                    <span className="text-gray-900">Portfolio Ready with Recommendations</span>
                  </>
                ) : (
                  <>
                    <div className="flex items-center justify-center w-8 h-8 bg-green-100 rounded-full">
                      <CheckCircle className="h-4 w-4 text-green-600" />
                    </div>
                    <span className="text-gray-900">Portfolio Validation Passed</span>
                  </>
                )}
              </DialogTitle>
              <DialogDescription className="text-sm text-gray-600">
                {hasErrors ? (
                  "Please fix the following issues before publishing your portfolio."
                ) : hasOnlyWarnings ? (
                  "Your portfolio is ready to publish! Consider these recommendations to make it even better."
                ) : (
                  "Your portfolio looks great and is ready to be published!"
                )}
              </DialogDescription>
            </DialogHeader>

            {/* Custom Close Button */}
            <button
              onClick={onClose}
              className="flex items-center justify-center w-8 h-8 rounded-full bg-gray-100 hover:bg-gray-200 transition-colors duration-200 text-gray-500 hover:text-gray-700"
              aria-label="Close modal"
            >
              <X className="h-4 w-4" />
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="px-6 py-4 overflow-y-auto max-h-[calc(85vh-140px)]">
          <ValidationErrorDisplay
            errors={validationResult.errors}
            warnings={validationResult.warnings}
            showWarnings={true}
          />
        </div>

        {/* Footer */}
        <div className="px-6 py-4 border-t bg-gray-50/30">
          <div className="flex justify-end space-x-3">

            {hasOnlyWarnings && allowProceedWithWarnings && onProceedAnyway && (
              <Button
                onClick={onProceedAnyway}
                className="bg-brandAccent hover:bg-brandAccent/90 text-white px-6"
              >
                Publish Anyway
              </Button>
            )}

            {!hasErrors && !hasWarnings && (
              <Button
                onClick={onClose}
                className="bg-brandAccent hover:bg-brandAccent/90 text-white px-6"
              >
                Continue
              </Button>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}

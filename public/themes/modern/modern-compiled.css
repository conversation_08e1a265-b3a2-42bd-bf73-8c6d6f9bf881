/* Modern Theme - Compiled CSS */

/* ===== NAVBAR STYLES ===== */
/* Modern Theme - Navbar Component (Dark Mode) */

.theme-modern-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 50;
  background: rgba(31, 41, 55, 0.95);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(75, 85, 99, 0.3);
  transition: all 0.3s ease;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.theme-modern-navbar-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  max-width: 1200px;
  margin: 0 auto;
}

.theme-modern-navbar-brand {
  font-size: 1.75rem;
  font-weight: 800;
  background: linear-gradient(135deg, #60a5fa 0%, #a78bfa 50%, #f472b6 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  text-decoration: none;
  transition: all 0.3s ease;
  letter-spacing: -0.025em;
}

.theme-modern-navbar-brand:hover {
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 50%, #ec4899 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  transform: scale(1.05);
}

.theme-modern-navbar-nav {
  display: none;
  gap: 2.5rem;
  align-items: center;
}

@media (min-width: 768px) {
  .theme-modern-navbar-nav {
    display: flex;
  }
}

.theme-modern-navbar-link {
  color: #d1d5db;
  text-decoration: none;
  font-weight: 600;
  font-size: 0.95rem;
  transition: all 0.3s ease;
  position: relative;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
}

.theme-modern-navbar-link:hover {
  color: #ffffff;
  background: rgba(59, 130, 246, 0.1);
  transform: translateY(-1px);
}

.theme-modern-navbar-link::after {
  content: '';
  position: absolute;
  bottom: 0.25rem;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 2px;
  background: linear-gradient(90deg, #60a5fa, #a78bfa, #f472b6);
  transition: width 0.3s ease;
  border-radius: 1px;
}

.theme-modern-navbar-link:hover::after {
  width: 80%;
}

.theme-modern-navbar-mobile-toggle {
  display: block;
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.5rem;
}

.theme-modern-navbar-mobile-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(59, 130, 246, 0.1);
  border: 1px solid rgba(59, 130, 246, 0.2);
  cursor: pointer;
  padding: 0.75rem;
  color: #d1d5db;
  border-radius: 0.5rem;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.theme-modern-navbar-mobile-btn:hover {
  background: rgba(59, 130, 246, 0.2);
  color: #ffffff;
  border-color: rgba(59, 130, 246, 0.4);
  transform: scale(1.05);
}

.theme-modern-navbar-mobile-icon {
  width: 1.5rem;
  height: 1.5rem;
}

@media (min-width: 768px) {
  .theme-modern-navbar-mobile-toggle {
    display: none;
  }
}

/* Mobile menu */
.theme-modern-navbar-mobile-menu {
  display: none;
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: rgba(31, 41, 55, 0.98);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(75, 85, 99, 0.3);
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.3);
  border-radius: 0 0 1rem 1rem;
  margin: 0 1rem;
}

.theme-modern-navbar-mobile-menu.active {
  display: block;
  animation: slideDown 0.3s ease-out;
}

.theme-modern-navbar-mobile-nav {
  display: flex;
  flex-direction: column;
  padding: 1.5rem;
  gap: 0.5rem;
}

.theme-modern-navbar-mobile-link {
  color: #d1d5db;
  text-decoration: none;
  font-weight: 600;
  padding: 1rem;
  transition: all 0.3s ease;
  border-radius: 0.5rem;
  border: 1px solid transparent;
}

.theme-modern-navbar-mobile-link:hover {
  color: #ffffff;
  background: rgba(59, 130, 246, 0.1);
  border-color: rgba(59, 130, 246, 0.2);
  transform: translateX(0.5rem);
}

/* Focus styles for navbar links */
.theme-modern-navbar-link:focus,
.theme-modern-navbar-mobile-link:focus,
.theme-modern-navbar-brand:focus {
  outline: none;
  color: #ffffff;
}

.theme-modern-navbar-link:focus::after {
  width: 80%;
}

.theme-modern-navbar-mobile-link:focus {
  background: rgba(59, 130, 246, 0.2);
  border-color: rgba(59, 130, 246, 0.4);
}

/* Animations */
@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive adjustments */
@media (max-width: 767px) {
  .theme-modern-navbar-container {
    padding: 1rem;
  }

  .theme-modern-navbar-brand {
    font-size: 1.5rem;
  }

  .theme-modern-navbar-mobile-menu {
    margin: 0 0.5rem;
  }
}

@media (min-width: 1024px) {
  .theme-modern-navbar-container {
    padding: 1.5rem 2rem;
  }

  .theme-modern-navbar-nav {
    gap: 3rem;
  }
}


/* ===== HERO STYLES ===== */
/* Modern Theme - Hero Component (Dark Mode) */

.theme-modern-hero {
  min-height: 100vh;
  display: flex;
  align-items: center;
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 25%, #334155 50%, #1e293b 75%, #0f172a 100%);
  color: white;
  position: relative;
  overflow: hidden;
  padding: 2rem 0;
}

.theme-modern-hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 20% 80%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
              radial-gradient(circle at 80% 20%, rgba(139, 92, 246, 0.1) 0%, transparent 50%),
              radial-gradient(circle at 40% 40%, rgba(244, 114, 182, 0.05) 0%, transparent 50%);
  opacity: 0.8;
}

.theme-modern-hero::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="2" cy="2" r="1" fill="rgba(96,165,250,0.1)"/><circle cx="12" cy="12" r="0.5" fill="rgba(167,139,250,0.1)"/><circle cx="18" cy="6" r="0.5" fill="rgba(244,114,182,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
  opacity: 0.4;
}

.theme-modern-hero-container {
  position: relative;
  z-index: 10;
  text-align: center;
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem 2rem;
}

.theme-modern-hero-title {
  font-size: 3rem;
  font-weight: 900;
  margin-bottom: 1.5rem;
  line-height: 1.1;
  color: #ffffff;
  letter-spacing: -0.025em;
  text-shadow: 0 4px 20px rgba(96, 165, 250, 0.3);
}

/* Gradient effect only when not editing */
.theme-modern-hero-title:not([contenteditable="true"]) {
  background: linear-gradient(135deg, #ffffff 0%, #e2e8f0 25%, #60a5fa 50%, #a78bfa 75%, #f472b6 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

@media (min-width: 640px) {
  .theme-modern-hero-title {
    font-size: 4.5rem;
    letter-spacing: -0.05em;
  }
}

@media (min-width: 1024px) {
  .theme-modern-hero-title {
    font-size: 6rem;
    letter-spacing: -0.075em;
  }
}

.theme-modern-hero-subtitle {
  font-size: 1.25rem;
  margin-bottom: 2.5rem;
  padding: 0 0.5rem;
  color: #e2e8f0;
  max-width: 600px;
  font-weight: 400;
  line-height: 1.6;
  opacity: 0.9;
  overflow-wrap: break-word;
  word-break: break-word;
}

@media (min-width: 640px) {
  .theme-modern-hero-subtitle {
    font-size: 1.5rem;
  }
}

@media (min-width: 1024px) {
  .theme-modern-hero-subtitle {
    font-size: 1.75rem;
  }
}

/* Button Styles */
.theme-modern-btn {
  display: inline-flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem 2rem;
  border-radius: 0.75rem;
  text-decoration: none;
  font-weight: 600;
  font-size: 1rem;
  transition: all 0.3s ease;
  border: none;
  cursor: pointer;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  position: relative;
  overflow: hidden;
}

.theme-modern-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.theme-modern-btn:hover::before {
  left: 100%;
}

.theme-modern-btn-primary {
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 50%, #ec4899 100%);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
}

.theme-modern-btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
  background: linear-gradient(135deg, #2563eb 0%, #7c3aed 50%, #db2777 100%);
}

.theme-modern-btn-secondary {
  background: rgba(255, 255, 255, 0.1);
  color: #e2e8f0;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.theme-modern-btn-secondary:hover {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.theme-modern-hero-layout {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4rem;
  min-height: 100vh;
  justify-content: center;
  padding: 8rem 0;
}

@media (min-width: 1024px) {
  .theme-modern-hero-layout {
    flex-direction: row;
    gap: 6rem;
    padding: 8rem 0;
  }
}

.theme-modern-hero-content {
  text-align: center;
  max-width: 700px;
  flex: 1;
}

@media (min-width: 1024px) {
  .theme-modern-hero-content {
    text-align: left;
  }
}

.theme-modern-hero-actions {
  display: flex;
  flex-direction: column;
  gap: 1.25rem;
  align-items: center;
  margin-top: 1rem;
}

@media (min-width: 640px) {
  .theme-modern-hero-actions {
    flex-direction: row;
    justify-content: center;
    gap: 1.5rem;
  }
}

@media (min-width: 1024px) {
  .theme-modern-hero-actions {
    justify-content: flex-start;
  }
}

.theme-modern-hero-action-icon {
  width: 1.25rem;
  height: 1.25rem;
}

.theme-modern-hero-upload-btn {
  cursor: pointer;
}

.theme-modern-hero-upload-input {
  position: absolute;
  opacity: 0;
  pointer-events: none;
}

.theme-modern-spinner {
  animation: spin 1s linear infinite;
}

/* Hero Image Styles */
.theme-modern-hero-image-container {
  position: relative;
  flex-shrink: 0;
  order: -1;
}

@media (min-width: 1024px) {
  .theme-modern-hero-image-container {
    order: 0;
  }
}

.theme-modern-hero-image-wrapper {
  position: relative;
  width: 18rem;
  height: 18rem;
}

@media (min-width: 640px) {
  .theme-modern-hero-image-wrapper {
    width: 20rem;
    height: 20rem;
  }
}

@media (min-width: 1024px) {
  .theme-modern-hero-image-wrapper {
    width: 24rem;
    height: 24rem;
  }
}

.theme-modern-hero-image-wrapper::before {
  content: '';
  position: absolute;
  inset: -0.25rem;
  background: linear-gradient(135deg, #60a5fa 0%, #a78bfa 100%);
  border-radius: 50%;
  opacity: 0.3;
  transition: all 0.3s ease;
  animation: pulse-glow 3s ease-in-out infinite;
}

.theme-modern-hero-image-wrapper:hover::before {
  opacity: 0.5;
  transform: scale(1.05);
}

.theme-modern-hero-image-wrapper::after {
  content: '';
  position: absolute;
  inset: 0.5rem;
  background: #1e293b;
  border-radius: 50%;
  z-index: 1;
  border: 3px solid rgba(96, 165, 250, 0.3);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.theme-modern-hero-image {
  position: absolute;
  inset: 1rem;
  width: calc(100% - 2rem);
  height: calc(100% - 2rem);
  border-radius: 50%;
  object-fit: cover;
  z-index: 10;
  border: 2px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
}

.theme-modern-hero-image-wrapper:hover .theme-modern-hero-image {
  transform: scale(1.02);
  border-color: rgba(96, 165, 250, 0.4);
  box-shadow: 0 8px 32px rgba(96, 165, 250, 0.2);
}

.theme-modern-hero-upload-overlay {
  position: absolute;
  inset: 1rem;
  border-radius: 50%;
  background: rgba(30, 41, 59, 0.95);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1rem;
  font-weight: 600;
  opacity: 0;
  transition: all 0.3s ease;
  cursor: pointer;
  z-index: 20;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 2px solid rgba(96, 165, 250, 0.4);
}

.theme-modern-hero-image-wrapper:hover .theme-modern-hero-upload-overlay {
  opacity: 1;
  background: rgba(30, 41, 59, 0.98);
  border-color: rgba(96, 165, 250, 0.6);
  transform: scale(1.02);
}

.theme-modern-hero-upload-icon {
  width: 2rem;
  height: 2rem;
  margin-bottom: 0.5rem;
  color: #60a5fa;
}

.theme-modern-hero-upload-loading {
  position: absolute;
  inset: 1rem;
  background: rgba(30, 41, 59, 0.95);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 30;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 2px solid rgba(96, 165, 250, 0.4);
}

.theme-modern-hero-upload-loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1.5rem;
  color: white;
}

.theme-modern-hero-upload-spinner {
  width: 4rem;
  height: 4rem;
  color: #60a5fa;
  animation: spin 1s linear infinite;
}

.theme-modern-hero-upload-text {
  font-size: 1.125rem;
  font-weight: 600;
  background: linear-gradient(90deg, #60a5fa, #a78bfa, #f472b6);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

/* Animations */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Subtle pulse animation for profile image glow */
@keyframes pulse-glow {
  0%, 100% {
    opacity: 0.3;
  }
  50% {
    opacity: 0.6;
  }
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .theme-modern-hero-container {
    padding: 0 1rem;
  }

  .theme-modern-hero-layout {
    gap: 3rem;
    padding: 8rem 0 5rem 0; /* Extra top padding to prevent navbar overlap */
    min-height: calc(100vh - 4rem); /* Adjust min-height to account for navbar */
  }

  .theme-modern-hero-title {
    font-size: 2.5rem;
  }

  .theme-modern-hero-subtitle {
    font-size: 1.125rem;
  }

  .theme-modern-hero-image-wrapper {
    width: 16rem;
    height: 16rem;
  }
}

/* Tablet responsive adjustments */
@media (min-width: 641px) and (max-width: 1023px) {
  .theme-modern-hero-layout {
    padding: 7rem 0 5rem 0; /* Extra top padding for tablet sizes */
  }
}


/* ===== ABOUT STYLES ===== */
/* Modern Theme - About Component */

.theme-modern-about {
  padding: 6rem 1rem;
  position: relative;
}

.theme-modern-about-container {
  max-width: 1200px;
  margin: 0 auto;
  display: grid;
  gap: 4rem;
  align-items: center;
  position: relative;
  z-index: 1;
}

@media (min-width: 1024px) {
  .theme-modern-about-container {
    grid-template-columns: 1.2fr 1fr;
    gap: 6rem;
  }
}

.theme-modern-about-content {
  text-align: center;
}

@media (min-width: 1024px) {
  .theme-modern-about-content {
    text-align: left;
  }
}

.theme-modern-about-header {
  margin-bottom: 2rem;
}

.theme-modern-about-title {
  font-size: 2.5rem;
  font-weight: 900;
  color: #ffffff;
  margin-bottom: 1rem;
  line-height: 1.1;
  letter-spacing: -0.025em;
  background: linear-gradient(135deg, #ffffff 0%, #e2e8f0 25%, #60a5fa 50%, #a78bfa 75%, #f472b6 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

/* Solid color for editing mode */
.theme-modern-about-title[contenteditable="true"] {
  color: #ffffff;
  background: none;
  -webkit-text-fill-color: #ffffff;
}

@media (min-width: 640px) {
  .theme-modern-about-title {
    font-size: 3rem;
    letter-spacing: -0.05em;
  }
}

@media (min-width: 1024px) {
  .theme-modern-about-title {
    font-size: 3.5rem;
    letter-spacing: -0.075em;
  }
}

.theme-modern-about-divider {
  width: 4rem;
  height: 0.25rem;
  background: linear-gradient(90deg, #60a5fa 0%, #a78bfa 100%);
  border-radius: 0.125rem;
  margin: 0 auto;
}

@media (min-width: 1024px) {
  .theme-modern-about-divider {
    margin: 0;
  }
}

.theme-modern-about-body {
  max-width: 600px;
  margin: 0 auto;
}

@media (min-width: 1024px) {
  .theme-modern-about-body {
    margin: 0;
  }
}

.theme-modern-about-text {
  font-size: 1.125rem;
  color: #cbd5e1;
  line-height: 1.8;
  margin: 0;
  font-weight: 400;
  opacity: 0.9;
}

@media (min-width: 640px) {
  .theme-modern-about-text {
    font-size: 1.25rem;
  }
}

.theme-modern-about-image-container {
  display: flex;
  justify-content: center;
  align-items: center;
}

.theme-modern-about-image-wrapper {
  position: relative;
  width: 100%;
  max-width: 400px;
  aspect-ratio: 1;
  border-radius: 1.5rem;
  overflow: hidden;
  background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
  padding: 0.5rem;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.5);
}

.theme-modern-about-image-wrapper::before {
  content: '';
  position: absolute;
  inset: -0.25rem;
  background: linear-gradient(135deg, #60a5fa 0%, #a78bfa 50%, #f472b6 100%);
  border-radius: 1.75rem;
  opacity: 0.3;
  z-index: -1;
  transition: all 0.3s ease;
}

.theme-modern-about-image-wrapper:hover::before {
  opacity: 0.5;
  transform: scale(1.02);
}

.theme-modern-about-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 1rem;
  transition: all 0.3s ease;
  border: 2px solid rgba(96, 165, 250, 0.2);
}

.theme-modern-about-image-wrapper:hover .theme-modern-about-image {
  transform: scale(1.02);
  border-color: rgba(96, 165, 250, 0.4);
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .theme-modern-about {
    padding: 4rem 1rem;
  }

  .theme-modern-about-container {
    gap: 3rem;
  }

  .theme-modern-about-title {
    font-size: 2rem;
  }

  .theme-modern-about-text {
    font-size: 1rem;
  }

  .theme-modern-about-image-wrapper {
    max-width: 300px;
  }
}

/* Qualifications Section */
.theme-modern-about-qualifications {
  margin-top: 2.5rem;
  padding-top: 2rem;
  border-top: 1px solid rgba(96, 165, 250, 0.2);
}

.theme-modern-about-qualifications-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: #ffffff;
  margin-bottom: 1.5rem;
  background: linear-gradient(135deg, #60a5fa 0%, #a78bfa 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

/* Solid color for editing mode */
.theme-modern-about-qualifications-title[contenteditable="true"] {
  color: #ffffff;
  background: none;
  -webkit-text-fill-color: #ffffff;
}

.theme-modern-about-qualifications-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.theme-modern-about-qualification-item {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  padding: 0.75rem 1rem;
  background: rgba(30, 41, 59, 0.5);
  border: 1px solid rgba(96, 165, 250, 0.2);
  border-radius: 0.75rem;
  transition: all 0.3s ease;
}

.theme-modern-about-qualification-item:hover {
  background: rgba(30, 41, 59, 0.7);
  border-color: rgba(96, 165, 250, 0.3);
  transform: translateY(-1px);
}

.theme-modern-about-qualification-bullet {
  color: #60a5fa;
  font-weight: 600;
  font-size: 1.25rem;
  line-height: 1;
  margin-top: 0.125rem;
  flex-shrink: 0;
}

.theme-modern-about-qualification-text {
  color: #cbd5e1;
  font-size: 1rem;
  line-height: 1.6;
  font-weight: 400;
  flex: 1;
  margin: 0;
}

@media (min-width: 640px) {
  .theme-modern-about-qualification-text {
    font-size: 1.125rem;
  }
}

/* Responsive adjustments for qualifications */
@media (max-width: 640px) {
  .theme-modern-about-qualifications {
    margin-top: 2rem;
    padding-top: 1.5rem;
  }

  .theme-modern-about-qualifications-title {
    font-size: 1.25rem;
    margin-bottom: 1rem;
  }

  .theme-modern-about-qualification-item {
    padding: 0.5rem 0.75rem;
    gap: 0.5rem;
  }

  .theme-modern-about-qualification-text {
    font-size: 0.875rem;
  }
}


/* ===== EXPERIENCE STYLES ===== */
/* Modern Theme - Experience Section - Completely Refactored */

/* Shared Editable Field Styles */
/* Base editable field styling - only applies when contenteditable */
.editable-field[contenteditable="true"] {
  position: relative;
  border: 2px dashed rgba(102, 126, 234, 0.3);
  border-radius: 0.375rem;
  padding: 0.5rem;
  background: rgba(102, 126, 234, 0.05);
  transition: all 0.3s ease;
  min-height: 1.5rem;
  cursor: text;
}

.editable-field[contenteditable="true"]:hover {
  border-color: rgba(102, 126, 234, 0.5);
  background: rgba(102, 126, 234, 0.1);
}

.editable-field[contenteditable="true"]:focus {
  outline: none;
  border-color: #667eea;
  background: rgba(102, 126, 234, 0.1);
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* Placeholder styling for empty editable fields */
.editable-field[contenteditable="true"]:empty:before {
  content: attr(data-placeholder);
  color: #9ca3af;
  font-style: italic;
  pointer-events: none;
}

/* Inline editable fields (for smaller text elements) - only applies when contenteditable */
.editable-field-inline[contenteditable="true"] {
  position: relative;
  border: 1px dashed rgba(102, 126, 234, 0.3);
  border-radius: 0.25rem;
  padding: 0.25rem 0.5rem;
  background: rgba(102, 126, 234, 0.05);
  transition: all 0.3s ease;
  min-height: 1rem;
  cursor: text;
  display: inline-block;
}

.editable-field-inline[contenteditable="true"]:hover {
  border-color: rgba(102, 126, 234, 0.5);
  background: rgba(102, 126, 234, 0.1);
}

.editable-field-inline[contenteditable="true"]:focus {
  outline: none;
  border-color: #667eea;
  background: rgba(102, 126, 234, 0.1);
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
}

.editable-field-inline[contenteditable="true"]:empty:before {
  content: attr(data-placeholder);
  color: #9ca3af;
  font-style: italic;
  pointer-events: none;
}

/* Large editable fields (for descriptions, content areas) - only applies when contenteditable */
.editable-field-large[contenteditable="true"] {
  position: relative;
  border: 2px dashed rgba(102, 126, 234, 0.3);
  border-radius: 0.5rem;
  padding: 1rem;
  background: rgba(102, 126, 234, 0.05);
  transition: all 0.3s ease;
  min-height: 3rem;
  cursor: text;
}

.editable-field-large[contenteditable="true"]:hover {
  border-color: rgba(102, 126, 234, 0.5);
  background: rgba(102, 126, 234, 0.1);
}

.editable-field-large[contenteditable="true"]:focus {
  outline: none;
  border-color: #667eea;
  background: rgba(102, 126, 234, 0.1);
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.editable-field-large[contenteditable="true"]:empty:before {
  content: attr(data-placeholder);
  color: #9ca3af;
  font-style: italic;
  pointer-events: none;
}

/* Modern theme variant (blue gradient) - only applies when contenteditable */
.editable-field-modern[contenteditable="true"] {
  border-color: rgba(59, 130, 246, 0.3);
  background: rgba(59, 130, 246, 0.05);
}

.editable-field-modern[contenteditable="true"]:hover {
  border-color: rgba(59, 130, 246, 0.5);
  background: rgba(59, 130, 246, 0.1);
}

.editable-field-modern[contenteditable="true"]:focus {
  border-color: #3b82f6;
  background: rgba(59, 130, 246, 0.1);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Main Section */
.modern-experience {
  padding: 5rem 0;
  position: relative;
}

.modern-experience-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
  position: relative;
  z-index: 1;
}

/* Section Header */
.modern-experience-header {
  text-align: center;
  margin-bottom: 4rem;
}

.modern-experience-title {
  font-size: 3rem;
  font-weight: 900;
  color: #ffffff;
  margin-bottom: 1rem;
  line-height: 1.1;
  letter-spacing: -0.025em;
  background: linear-gradient(135deg, #ffffff 0%, #e2e8f0 25%, #60a5fa 50%, #a78bfa 75%, #f472b6 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

@media (min-width: 640px) {
  .modern-experience-title {
    font-size: 3.5rem;
    letter-spacing: -0.05em;
  }
}

@media (min-width: 1024px) {
  .modern-experience-title {
    font-size: 4rem;
    letter-spacing: -0.075em;
  }
}

.modern-experience-divider {
  width: 4rem;
  height: 0.25rem;
  background: linear-gradient(90deg, #60a5fa 0%, #a78bfa 100%);
  border-radius: 0.125rem;
  margin: 0 auto 1.5rem;
}

.modern-experience-subtitle {
  font-size: 1.25rem;
  color: #cbd5e1;
  line-height: 1.6;
  max-width: 600px;
  margin: 0 auto;
  opacity: 0.9;
}

@media (min-width: 768px) {
  .modern-experience-subtitle {
    font-size: 1.375rem;
  }
}

/* Experience Grid */
.modern-experience-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 2rem;
  max-width: 800px;
  margin: 0 auto;
}

@media (min-width: 768px) {
  .modern-experience-grid {
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2.5rem;
  }
}

/* Experience Cards */
.modern-experience-card {
  background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
  padding: 2rem;
  border-radius: 1rem;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(96, 165, 250, 0.2);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.modern-experience-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(96, 165, 250, 0.05) 0%, rgba(168, 139, 250, 0.05) 100%);
  pointer-events: none;
}

.modern-experience-card::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: linear-gradient(180deg, #60a5fa 0%, #a78bfa 100%);
  opacity: 0.7;
  transition: opacity 0.3s ease;
}

.modern-experience-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.4);
  border-color: rgba(96, 165, 250, 0.4);
}

.modern-experience-card:hover::after {
  opacity: 1;
}

/* Card Header */
.modern-experience-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1.5rem;
  position: relative;
  z-index: 1;
}

.modern-experience-role-section {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  flex: 1;
  min-width: 0;
}

.modern-experience-briefcase-icon {
  color: #60a5fa;
  flex-shrink: 0;
  margin-top: 0.25rem;
}

.modern-experience-role-content {
  flex: 1;
  min-width: 0;
}

/* Role Styling */
.modern-experience-role {
  font-size: 1.375rem;
  font-weight: 700;
  color: #ffffff;
  margin: 0 0 0.5rem 0;
  line-height: 1.3;
}



/* Company Styling */
.modern-experience-company {
  font-size: 1rem;
  color: #60a5fa;
  font-weight: 600;
  margin: 0;
  line-height: 1.4;
}



/* Delete Button */
.modern-experience-delete-btn {
  background: rgb(237, 96, 96);
  border: 1px solid rgba(239, 68, 68, 0.3);
  color: #f3eeee;
  padding: 0.5rem;
  border-radius: 0.5rem;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  margin: 0 1rem;
}

.modern-experience-delete-btn:hover {
  background: rgba(239, 68, 68, 0.2);
  border-color: rgba(242, 74, 74, 0.953);
  transform: scale(1.05);
}

/* Meta Information */
.modern-experience-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  margin-bottom: 1.5rem;
  position: relative;
  z-index: 1;
}

.modern-experience-duration,
.modern-experience-location {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.modern-experience-meta-icon {
  color: #94a3b8;
  flex-shrink: 0;
}

.modern-experience-duration-text,
.modern-experience-location-text {
  font-size: 0.875rem;
  color: #e2e8f0;
  font-weight: 500;
  margin: 0;
}



/* Description */
.modern-experience-description {
  position: relative;
  z-index: 1;
}

.modern-experience-description-text {
  font-size: 1rem;
  color: #cbd5e1;
  line-height: 1.7;
  margin: 0;
  white-space: pre-line;
}



/* Add Experience Section */
.modern-experience-add-section {
  text-align: center;
  margin-top: 3rem;
}

.modern-experience-add-btn {
  background: linear-gradient(135deg, #60a5fa 0%, #a78bfa 100%);
  border: none;
  color: #ffffff;
  padding: 1rem 2rem;
  border-radius: 0.75rem;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  box-shadow: 0 4px 20px rgba(96, 165, 250, 0.3);
}

.modern-experience-add-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 30px rgba(96, 165, 250, 0.4);
}

/* Responsive Design */
@media (max-width: 640px) {
  .modern-experience {
    padding: 3rem 0;
  }

  .modern-experience-title {
    font-size: 2.5rem;
  }

  .modern-experience-card {
    padding: 1.5rem;
  }

  .modern-experience-role {
    font-size: 1.25rem;
  }

  .modern-experience-header {
    flex-direction: column;
    gap: 1rem;
  }

  .modern-experience-delete-btn {
    align-self: flex-end;
  }
}


/* ===== SKILLS STYLES ===== */
/* Modern Theme - Skills Section - Completely Refactored */

/* Shared Editable Field Styles */
/* Base editable field styling - only applies when contenteditable */
.editable-field[contenteditable="true"] {
  position: relative;
  border: 2px dashed rgba(102, 126, 234, 0.3);
  border-radius: 0.375rem;
  padding: 0.5rem;
  background: rgba(102, 126, 234, 0.05);
  transition: all 0.3s ease;
  min-height: 1.5rem;
  cursor: text;
}

.editable-field[contenteditable="true"]:hover {
  border-color: rgba(102, 126, 234, 0.5);
  background: rgba(102, 126, 234, 0.1);
}

.editable-field[contenteditable="true"]:focus {
  outline: none;
  border-color: #667eea;
  background: rgba(102, 126, 234, 0.1);
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* Placeholder styling for empty editable fields */
.editable-field[contenteditable="true"]:empty:before {
  content: attr(data-placeholder);
  color: #9ca3af;
  font-style: italic;
  pointer-events: none;
}

/* Inline editable fields (for smaller text elements) - only applies when contenteditable */
.editable-field-inline[contenteditable="true"] {
  position: relative;
  border: 1px dashed rgba(102, 126, 234, 0.3);
  border-radius: 0.25rem;
  padding: 0.25rem 0.5rem;
  background: rgba(102, 126, 234, 0.05);
  transition: all 0.3s ease;
  min-height: 1rem;
  cursor: text;
  display: inline-block;
}

.editable-field-inline[contenteditable="true"]:hover {
  border-color: rgba(102, 126, 234, 0.5);
  background: rgba(102, 126, 234, 0.1);
}

.editable-field-inline[contenteditable="true"]:focus {
  outline: none;
  border-color: #667eea;
  background: rgba(102, 126, 234, 0.1);
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
}

.editable-field-inline[contenteditable="true"]:empty:before {
  content: attr(data-placeholder);
  color: #9ca3af;
  font-style: italic;
  pointer-events: none;
}

/* Modern theme variant (blue gradient) - only applies when contenteditable */
.editable-field-modern[contenteditable="true"] {
  border-color: rgba(59, 130, 246, 0.3);
  background: rgba(59, 130, 246, 0.05);
}

.editable-field-modern[contenteditable="true"]:hover {
  border-color: rgba(59, 130, 246, 0.5);
  background: rgba(59, 130, 246, 0.1);
}

.editable-field-modern[contenteditable="true"]:focus {
  border-color: #3b82f6;
  background: rgba(59, 130, 246, 0.1);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Main Section */
.modern-skills {
  padding: 5rem 0;
  position: relative;
}

.modern-skills-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
  position: relative;
  z-index: 1;
}

/* Section Header */
.modern-skills-header {
  text-align: center;
  margin-bottom: 4rem;
}

.modern-skills-header-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 4rem;
  height: 4rem;
  background: linear-gradient(135deg, #60a5fa 0%, #a78bfa 100%);
  border-radius: 1rem;
  margin-bottom: 1.5rem;
  color: #ffffff;
  box-shadow: 0 8px 32px rgba(96, 165, 250, 0.3);
}

.modern-skills-title {
  font-size: 3rem;
  font-weight: 900;
  color: #ffffff;
  margin-bottom: 1rem;
  line-height: 1.1;
  letter-spacing: -0.025em;
  background: linear-gradient(135deg, #ffffff 0%, #e2e8f0 25%, #60a5fa 50%, #a78bfa 75%, #f472b6 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

@media (min-width: 640px) {
  .modern-skills-title {
    font-size: 3.5rem;
    letter-spacing: -0.05em;
  }
}

@media (min-width: 1024px) {
  .modern-skills-title {
    font-size: 4rem;
    letter-spacing: -0.075em;
  }
}

.modern-skills-divider {
  width: 4rem;
  height: 0.25rem;
  background: linear-gradient(90deg, #60a5fa 0%, #a78bfa 100%);
  border-radius: 0.125rem;
  margin: 0 auto 1.5rem;
}

.modern-skills-subtitle {
  font-size: 1.25rem;
  color: #cbd5e1;
  line-height: 1.6;
  max-width: 600px;
  margin: 0 auto;
  opacity: 0.9;
}

@media (min-width: 768px) {
  .modern-skills-subtitle {
    font-size: 1.375rem;
  }
}

/* Skills Content */
.modern-skills-content {
  max-width: 1000px;
  margin: 0 auto;
}

/* Skills Grid */
.modern-skills-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  align-items: start;
}

@media (min-width: 640px) {
  .modern-skills-grid {
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    gap: 2rem;
  }
}

@media (min-width: 1024px) {
  .modern-skills-grid {
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  }
}

/* Skill Badges */
.modern-skill-badge {
  background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
  border-radius: 0.75rem;
  padding: 1.25rem;
  border: 1px solid rgba(96, 165, 250, 0.2);
  transition: all 0.3s ease;
  position: relative;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
  max-width: 300px;
}

.modern-skill-badge::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(96, 165, 250, 0.05) 0%, rgba(168, 139, 250, 0.05) 100%);
  border-radius: 0.75rem;
  pointer-events: none;
}

.modern-skill-badge:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.3);
  border-color: rgba(96, 165, 250, 0.4);
}

.modern-skill-badge-content {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  position: relative;
  z-index: 1;
}

.modern-skill-badge-icon {
  color: #60a5fa;
  flex-shrink: 0;
}

.modern-skill-badge-text {
  font-size: 1rem;
  font-weight: 600;
  color: #ffffff;
  flex: 1;
  line-height: 1.4;
  overflow-wrap: break-word;
  word-break: break-word;
}

/* Delete Button */
.modern-skill-badge-delete {
  background: rgb(237, 96, 96);
  border: 1px solid rgba(239, 68, 68, 0.3);
  color: #ffffff;
  padding: 0.375rem;
  border-radius: 0.375rem;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.modern-skill-badge-delete:hover {
  background: rgba(239, 68, 68, 0.8);
  transform: scale(1.05);
}

/* Add Skill Section */
.modern-skills-add-section {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 120px;
  border: 2px dashed rgba(96, 165, 250, 0.3);
  border-radius: 0.75rem;
  background: rgba(96, 165, 250, 0.05);
  transition: all 0.3s ease;
}

.modern-skills-add-section:hover {
  border-color: rgba(96, 165, 250, 0.5);
  background: rgba(96, 165, 250, 0.1);
}

.modern-skills-add-btn {
  background: linear-gradient(135deg, #60a5fa 0%, #a78bfa 100%);
  border: none;
  color: #ffffff;
  padding: 1rem 1.5rem;
  border-radius: 0.5rem;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  box-shadow: 0 4px 20px rgba(96, 165, 250, 0.3);
}

.modern-skills-add-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 30px rgba(96, 165, 250, 0.4);
}

/* Enhanced Editing Experience */
.modern-skill-badge-text.editable-field-inline {
  min-height: 1.5rem;
  display: flex;
  align-items: center;
  font-size: 1rem;
  font-weight: 600;
}

/* Better spacing for editing mode */
.modern-skill-badge-content {
  min-height: 2.5rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .modern-skills {
    padding: 3rem 0;
  }

  .modern-skills-title {
    font-size: 2.5rem;
  }

  .modern-skills-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .modern-skill-badge {
    padding: 1rem;
  }

  .modern-skill-badge-content {
    gap: 0.5rem;
  }

  .modern-skills-header-icon {
    width: 3rem;
    height: 3rem;
  }
}

@media (max-width: 480px) {
  .modern-skills-header {
    margin-bottom: 3rem;
  }

  .modern-skills-title {
    font-size: 2rem;
  }

  .modern-skills-subtitle {
    font-size: 1.125rem;
  }

  .modern-skill-badge {
    padding: 0.875rem;
  }

  .modern-skill-badge-text {
    font-size: 0.9rem;
  }

  .modern-skills-add-btn {
    padding: 0.875rem 1.25rem;
    font-size: 0.9rem;
  }
}


/* ===== PROJECTS STYLES ===== */
/* Modern Theme - Projects Section - Completely Refactored */

/* Shared Editable Field Styles */
/* Base editable field styling - only applies when contenteditable */
.editable-field[contenteditable="true"] {
  position: relative;
  border: 2px dashed rgba(102, 126, 234, 0.3);
  border-radius: 0.375rem;
  padding: 0.5rem;
  background: rgba(102, 126, 234, 0.05);
  transition: all 0.3s ease;
  min-height: 1.5rem;
  cursor: text;
}

.editable-field[contenteditable="true"]:hover {
  border-color: rgba(102, 126, 234, 0.5);
  background: rgba(102, 126, 234, 0.1);
}

.editable-field[contenteditable="true"]:focus {
  outline: none;
  border-color: #667eea;
  background: rgba(102, 126, 234, 0.1);
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* Placeholder styling for empty editable fields */
.editable-field[contenteditable="true"]:empty:before {
  content: attr(data-placeholder);
  color: #9ca3af;
  font-style: italic;
  pointer-events: none;
}

/* Inline editable fields (for smaller text elements) - only applies when contenteditable */
.editable-field-inline[contenteditable="true"] {
  position: relative;
  border: 1px dashed rgba(102, 126, 234, 0.3);
  border-radius: 0.25rem;
  padding: 0.25rem 0.5rem;
  background: rgba(102, 126, 234, 0.05);
  transition: all 0.3s ease;
  min-height: 1rem;
  cursor: text;
  display: inline-block;
}

.editable-field-inline[contenteditable="true"]:hover {
  border-color: rgba(102, 126, 234, 0.5);
  background: rgba(102, 126, 234, 0.1);
}

.editable-field-inline[contenteditable="true"]:focus {
  outline: none;
  border-color: #667eea;
  background: rgba(102, 126, 234, 0.1);
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
}

.editable-field-inline[contenteditable="true"]:empty:before {
  content: attr(data-placeholder);
  color: #9ca3af;
  font-style: italic;
  pointer-events: none;
}

/* Large editable fields (for descriptions, content areas) - only applies when contenteditable */
.editable-field-large[contenteditable="true"] {
  position: relative;
  border: 2px dashed rgba(102, 126, 234, 0.3);
  border-radius: 0.5rem;
  padding: 1rem;
  background: rgba(102, 126, 234, 0.05);
  transition: all 0.3s ease;
  min-height: 3rem;
  cursor: text;
}

.editable-field-large[contenteditable="true"]:hover {
  border-color: rgba(102, 126, 234, 0.5);
  background: rgba(102, 126, 234, 0.1);
}

.editable-field-large[contenteditable="true"]:focus {
  outline: none;
  border-color: #667eea;
  background: rgba(102, 126, 234, 0.1);
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.editable-field-large[contenteditable="true"]:empty:before {
  content: attr(data-placeholder);
  color: #9ca3af;
  font-style: italic;
  pointer-events: none;
}

/* Modern theme variant (blue gradient) - only applies when contenteditable */
.editable-field-modern[contenteditable="true"] {
  border-color: rgba(59, 130, 246, 0.3);
  background: rgba(59, 130, 246, 0.05);
}

.editable-field-modern[contenteditable="true"]:hover {
  border-color: rgba(59, 130, 246, 0.5);
  background: rgba(59, 130, 246, 0.1);
}

.editable-field-modern[contenteditable="true"]:focus {
  border-color: #3b82f6;
  background: rgba(59, 130, 246, 0.1);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Ensure long project titles wrap correctly */
.modern-project-title {
  overflow-wrap: break-word;
  word-break: break-word; /* For better browser compatibility */
  min-width: 0; /* Allow the element to shrink and wrap */
}

/* Main Section */
.modern-projects {
  padding: 5rem 0;
  position: relative;
}

.modern-projects-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
  position: relative;
  z-index: 1;
}

/* Section Header */
.modern-projects-header {
  text-align: center;
  margin-bottom: 4rem;
}

.modern-projects-title {
  font-size: 3rem;
  font-weight: 900;
  color: #ffffff;
  margin-bottom: 1rem;
  line-height: 1.1;
  letter-spacing: -0.025em;
  background: linear-gradient(135deg, #ffffff 0%, #e2e8f0 25%, #60a5fa 50%, #a78bfa 75%, #f472b6 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

@media (min-width: 640px) {
  .modern-projects-title {
    font-size: 3.5rem;
    letter-spacing: -0.05em;
  }
}

@media (min-width: 1024px) {
  .modern-projects-title {
    font-size: 4rem;
    letter-spacing: -0.075em;
  }
}

.modern-projects-divider {
  width: 4rem;
  height: 0.25rem;
  background: linear-gradient(90deg, #60a5fa 0%, #a78bfa 100%);
  border-radius: 0.125rem;
  margin: 0 auto 1.5rem;
}

.modern-projects-subtitle {
  font-size: 1.25rem;
  color: #cbd5e1;
  line-height: 1.6;
  max-width: 600px;
  margin: 0 auto;
  opacity: 0.9;
}

@media (min-width: 768px) {
  .modern-projects-subtitle {
    font-size: 1.375rem;
  }
}

/* Projects Grid */
.modern-projects-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 2rem;
  max-width: 1000px;
  margin: 0 auto;
}

@media (min-width: 768px) {
  .modern-projects-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 2.5rem;
  }
}

@media (min-width: 1024px) {
  .modern-projects-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

/* Project Cards */
.modern-project-card {
  background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
  border-radius: 1rem;
  overflow: hidden;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(96, 165, 250, 0.2);
  transition: all 0.3s ease;
  position: relative;
}

.modern-project-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(96, 165, 250, 0.05) 0%, rgba(168, 139, 250, 0.05) 100%);
  pointer-events: none;
}

.modern-project-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 25px 60px rgba(0, 0, 0, 0.4);
  border-color: rgba(96, 165, 250, 0.4);
}

/* Project Image */
.modern-project-image-container {
  position: relative;
  width: 100%;
  height: 250px;
  overflow: hidden;
  background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
}

.modern-project-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: all 0.3s ease;
}

.modern-project-image-uploading {
  opacity: 0.5;
}

/* Upload Overlay */
.modern-project-upload-overlay {
  position: absolute;
  inset: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
  cursor: pointer;
  z-index: 10;
}

.modern-project-card:hover .modern-project-upload-overlay {
  opacity: 1;
}

.modern-project-upload-input {
  position: absolute;
  inset: 0;
  opacity: 0;
  cursor: pointer;
}

.modern-project-upload-content,
.modern-project-upload-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  color: #ffffff;
}

.modern-project-upload-icon {
  width: 2rem;
  height: 2rem;
}

.modern-project-upload-text {
  font-size: 0.875rem;
  font-weight: 500;
}

/* Loading States */
.modern-project-loading-overlay {
  position: absolute;
  inset: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 20;
}

.modern-project-loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  color: #ffffff;
}

.modern-project-loading-spinner {
  width: 2rem;
  height: 2rem;
  animation: spin 1s linear infinite;
}

.modern-project-loading-text {
  font-size: 0.875rem;
  font-weight: 500;
}

/* Spinner Animation */
@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.modern-spinner {
  animation: spin 1s linear infinite;
}

/* Project Content */
.modern-project-content {
  padding: 1.5rem;
  position: relative;
  z-index: 1;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

/* Project Header */
.modern-project-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
  gap: 0.5rem;
}

.modern-project-title-section {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  flex: 1;
  min-width: 0;
}

.modern-project-folder-icon {
  color: #60a5fa;
  flex-shrink: 0;
}

/* Project Title */
.modern-project-title {
  font-size: 1.25rem;
  font-weight: 700;
  color: #ffffff;
  margin: 0;
  line-height: 1.4;
  flex: 1;
}

/* Delete Button */
.modern-project-delete-btn {
  background: rgb(237, 96, 96);
  border: 1px solid rgba(239, 68, 68, 0.3);
  color: #ffffff;
  padding: 0.5rem;
  border-radius: 0.375rem;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  width: 2.5rem;
  height: 2.5rem;
}

.modern-project-delete-btn:hover {
  background: rgba(239, 68, 68, 0.2);
  border-color: rgba(239, 68, 68, 0.5);
  transform: scale(1.05);
}

/* Project Description */
.modern-project-description {
  flex: 1;
}

.modern-project-description-text {
  font-size: 0.95rem;
  color: #cbd5e1;
  line-height: 1.6;
  margin: 0;
  white-space: pre-line;
  min-height: 4rem;
  overflow-wrap: break-word;
  word-break: break-word;
}

/* Project URL */
.modern-project-url {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-top: auto;
}

.modern-project-url-input {
  flex: 1;
  min-width: 0;
  font-size: 0.875rem;
  color: #94a3b8;
}

.modern-project-link {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  color: #60a5fa;
  text-decoration: none;
  font-weight: 500;
  font-size: 0.875rem;
  padding: 0.5rem 0.75rem;
  background: rgba(96, 165, 250, 0.1);
  border: 1px solid rgba(96, 165, 250, 0.3);
  border-radius: 0.375rem;
  transition: all 0.3s ease;
  flex-shrink: 0;
  white-space: nowrap;
}

.modern-project-link:hover {
  background: rgba(96, 165, 250, 0.2);
  border-color: rgba(96, 165, 250, 0.5);
  transform: translateY(-1px);
}

/* Add Project Section */
.modern-projects-add-section {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  border: 2px dashed rgba(96, 165, 250, 0.3);
  border-radius: 1rem;
  background: rgba(96, 165, 250, 0.05);
  transition: all 0.3s ease;
}

.modern-projects-add-section:hover {
  border-color: rgba(96, 165, 250, 0.5);
  background: rgba(96, 165, 250, 0.1);
}

.modern-projects-add-btn {
  background: linear-gradient(135deg, #60a5fa 0%, #a78bfa 100%);
  border: none;
  color: #ffffff;
  padding: 1.5rem 2rem;
  border-radius: 0.75rem;
  font-size: 1.125rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  box-shadow: 0 4px 20px rgba(96, 165, 250, 0.3);
}

.modern-projects-add-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 30px rgba(96, 165, 250, 0.4);
}

/* Enhanced Editing Experience */
.modern-project-card {
  min-height: 480px;
  display: flex;
  flex-direction: column;
}

.modern-project-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 1.25rem;
}

/* Better spacing for editable fields */
.modern-project-title.editable-field {
  min-height: 2.5rem;
  display: flex;
  align-items: center;
  font-size: 1.25rem;
}

.modern-project-description-text.editable-field-large {
  min-height: 6rem;
  line-height: 1.6;
  font-size: 0.95rem;
}

.modern-project-url-input.editable-field-inline {
  min-height: 2.5rem;
  display: flex;
  align-items: center;
  font-size: 0.875rem;
}

/* Improved visual hierarchy in edit mode */
.modern-project-header {
  border-bottom: 1px solid rgba(96, 165, 250, 0.15);
  padding-bottom: 1rem;
}

.modern-project-url {
  border-top: 1px solid rgba(96, 165, 250, 0.15);
  padding-top: 1rem;
  margin-top: auto;
}

/* Enhanced hover states for better UX */
.modern-project-card:hover .modern-project-header {
  border-bottom-color: rgba(96, 165, 250, 0.25);
}

.modern-project-card:hover .modern-project-url {
  border-top-color: rgba(96, 165, 250, 0.25);
}

/* Responsive Design */
@media (max-width: 768px) {
  .modern-projects {
    padding: 3rem 0;
  }

  .modern-projects-title {
    font-size: 2.5rem;
  }

  .modern-project-content {
    padding: 1.25rem;
    gap: 0.75rem;
  }

  .modern-project-title {
    font-size: 1.125rem;
  }

  .modern-project-header {
    flex-wrap: wrap;
    gap: 0.75rem;
  }

  .modern-project-title-section {
    flex: 1;
    min-width: 200px;
  }

  .modern-project-url {
    flex-direction: column;
    align-items: stretch;
    gap: 0.75rem;
  }

  .modern-project-link {
    justify-content: center;
  }

  .modern-project-description-text.editable-field-large {
    min-height: 4rem;
  }
}

@media (max-width: 480px) {
  .modern-project-content {
    padding: 1rem;
  }

  .modern-project-header {
    flex-direction: column;
    align-items: stretch;
  }

  .modern-project-delete-btn {
    align-self: flex-end;
    margin-top: 0.5rem;
  }
}


/* ===== CONTACT STYLES ===== */
/* Modern Theme - Contact Section - Completely Refactored */

/* Shared Editable Field Styles */
/* Base editable field styling - only applies when contenteditable */
.editable-field[contenteditable="true"] {
  position: relative;
  border: 2px dashed rgba(102, 126, 234, 0.3);
  border-radius: 0.375rem;
  padding: 0.5rem;
  background: rgba(102, 126, 234, 0.05);
  transition: all 0.3s ease;
  min-height: 1.5rem;
  cursor: text;
}

.editable-field[contenteditable="true"]:hover {
  border-color: rgba(102, 126, 234, 0.5);
  background: rgba(102, 126, 234, 0.1);
}

.editable-field[contenteditable="true"]:focus {
  outline: none;
  border-color: #667eea;
  background: rgba(102, 126, 234, 0.1);
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* Placeholder styling for empty editable fields */
.editable-field[contenteditable="true"]:empty:before {
  content: attr(data-placeholder);
  color: #9ca3af;
  font-style: italic;
  pointer-events: none;
}

/* Inline editable fields (for smaller text elements) - only applies when contenteditable */
.editable-field-inline[contenteditable="true"] {
  position: relative;
  border: 1px dashed rgba(102, 126, 234, 0.3);
  border-radius: 0.25rem;
  padding: 0.25rem 0.5rem;
  background: rgba(102, 126, 234, 0.05);
  transition: all 0.3s ease;
  min-height: 1rem;
  cursor: text;
  display: inline-block;
}

.editable-field-inline[contenteditable="true"]:hover {
  border-color: rgba(102, 126, 234, 0.5);
  background: rgba(102, 126, 234, 0.1);
}

.editable-field-inline[contenteditable="true"]:focus {
  outline: none;
  border-color: #667eea;
  background: rgba(102, 126, 234, 0.1);
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
}

.editable-field-inline[contenteditable="true"]:empty:before {
  content: attr(data-placeholder);
  color: #9ca3af;
  font-style: italic;
  pointer-events: none;
}

/* Modern theme variant (blue gradient) - only applies when contenteditable */
.editable-field-modern[contenteditable="true"] {
  border-color: rgba(59, 130, 246, 0.3);
  background: rgba(59, 130, 246, 0.05);
}

.editable-field-modern[contenteditable="true"]:hover {
  border-color: rgba(59, 130, 246, 0.5);
  background: rgba(59, 130, 246, 0.1);
}

.editable-field-modern[contenteditable="true"]:focus {
  border-color: #3b82f6;
  background: rgba(59, 130, 246, 0.1);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Main Section */
.modern-contact {
  padding: 5rem 0;
  background: linear-gradient(135deg, #1e293b 0%, #0f172a 50%, #111827 100%);
  position: relative;
  overflow: hidden;
}

.modern-contact::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    radial-gradient(circle at 25% 25%, rgba(139, 92, 246, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(236, 72, 153, 0.1) 0%, transparent 50%);
  pointer-events: none;
}

.modern-contact-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
  position: relative;
  z-index: 1;
}

/* Section Header */
.modern-contact-header {
  text-align: center;
  margin-bottom: 4rem;
}

.modern-contact-header-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 4rem;
  height: 4rem;
  background: linear-gradient(135deg, #8b5cf6 0%, #ec4899 100%);
  border-radius: 1rem;
  margin-bottom: 1.5rem;
  color: #ffffff;
  box-shadow: 0 8px 32px rgba(139, 92, 246, 0.3);
}

.modern-contact-title {
  font-size: 3rem;
  font-weight: 900;
  color: #ffffff;
  margin-bottom: 1rem;
  line-height: 1.1;
  letter-spacing: -0.025em;
  background: linear-gradient(135deg, #ffffff 0%, #e2e8f0 25%, #8b5cf6 50%, #ec4899 75%, #f472b6 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

@media (min-width: 640px) {
  .modern-contact-title {
    font-size: 3.5rem;
    letter-spacing: -0.05em;
  }
}

@media (min-width: 1024px) {
  .modern-contact-title {
    font-size: 4rem;
    letter-spacing: -0.075em;
  }
}

.modern-contact-divider {
  width: 4rem;
  height: 0.25rem;
  background: linear-gradient(90deg, #8b5cf6 0%, #ec4899 100%);
  border-radius: 0.125rem;
  margin: 0 auto 1.5rem;
}

.modern-contact-subtitle {
  font-size: 1.25rem;
  color: #cbd5e1;
  line-height: 1.6;
  max-width: 600px;
  margin: 0 auto;
  opacity: 0.9;
}

@media (min-width: 768px) {
  .modern-contact-subtitle {
    font-size: 1.375rem;
  }
}

/* Contact Content */
.modern-contact-content {
  max-width: 1000px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  gap: 4rem;
}

/* Contact Info Grid */
.modern-contact-info-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 2rem;
}

@media (min-width: 768px) {
  .modern-contact-info-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 2.5rem;
  }
}

/* Contact Info Cards */
.modern-contact-info-card {
  background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
  border-radius: 1rem;
  padding: 2rem;
  border: 1px solid rgba(139, 92, 246, 0.2);
  transition: all 0.3s ease;
  position: relative;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  min-height: 180px;
  display: flex;
  flex-direction: column;
}

.modern-contact-info-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.05) 0%, rgba(236, 72, 153, 0.05) 100%);
  border-radius: 1rem;
  pointer-events: none;
}

.modern-contact-info-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 16px 48px rgba(0, 0, 0, 0.3);
  border-color: rgba(139, 92, 246, 0.4);
}

.modern-contact-info-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.5rem;
  position: relative;
  z-index: 1;
}

.modern-contact-info-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 3rem;
  height: 3rem;
  background: linear-gradient(135deg, #8b5cf6 0%, #ec4899 100%);
  border-radius: 0.75rem;
  color: #ffffff;
  flex-shrink: 0;
  box-shadow: 0 4px 16px rgba(139, 92, 246, 0.3);
}

.modern-contact-info-label {
  font-size: 1.25rem;
  font-weight: 700;
  color: #ffffff;
  margin: 0;
}

.modern-contact-info-content {
  position: relative;
  z-index: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1.25rem;
  flex: 1;
}

.modern-contact-info-text {
  font-size: 1rem;
  color: #cbd5e1;
  line-height: 1.6;
  word-break: break-word;
  padding: 0.75rem;
}

.modern-contact-info-link {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  color: #ffffff;
  text-decoration: none;
  font-weight: 600;
  font-size: 0.9rem;
  padding: 0.75rem 1.25rem;
  background: linear-gradient(135deg, #8b5cf6 0%, #ec4899 100%);
  border: none;
  border-radius: 0.5rem;
  transition: all 0.3s ease;
  align-self: stretch;
  margin-top: auto;
  box-shadow: 0 4px 16px rgba(139, 92, 246, 0.3);
}

.modern-contact-info-link:hover {
  background: linear-gradient(135deg, #7c3aed 0%, #db2777 100%);
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(139, 92, 246, 0.4);
}

/* Social Links Section */
.modern-contact-social-section {
  text-align: center;
}

.modern-contact-social-title {
  font-size: 2rem;
  font-weight: 800;
  color: #ffffff;
  margin-bottom: 2rem;
  background: linear-gradient(135deg, #8b5cf6 0%, #ec4899 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.modern-contact-social-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
  max-width: 800px;
  margin: 0 auto;
}

@media (min-width: 640px) {
  .modern-contact-social-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 2rem;
  }
}

@media (min-width: 1024px) {
  .modern-contact-social-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

/* Social Cards */
.modern-contact-social-card {
  background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
  border-radius: 1rem;
  padding: 2rem;
  border: 1px solid rgba(139, 92, 246, 0.2);
  transition: all 0.3s ease;
  position: relative;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
  min-height: 180px;
  display: flex;
  flex-direction: column;
}

.modern-contact-social-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.05) 0%, rgba(236, 72, 153, 0.05) 100%);
  border-radius: 1rem;
  pointer-events: none;
}

.modern-contact-social-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.3);
  border-color: rgba(139, 92, 246, 0.4);
}

.modern-contact-social-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.5rem;
  position: relative;
  z-index: 1;
}

.modern-contact-social-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 3rem;
  height: 3rem;
  background: linear-gradient(135deg, #8b5cf6 0%, #ec4899 100%);
  border-radius: 0.75rem;
  color: #ffffff;
  flex-shrink: 0;
  box-shadow: 0 4px 16px rgba(139, 92, 246, 0.3);
}

.modern-contact-social-label {
  font-size: 1.25rem;
  font-weight: 700;
  color: #ffffff;
  margin: 0;
}

.modern-contact-social-content {
  position: relative;
  z-index: 1;
  display: flex;
  flex-direction: column;
  gap: 1.25rem;
  flex: 1;
}

.modern-contact-social-url {
  font-size: 0.9rem;
  color: #cbd5e1;
  line-height: 1.5;
  word-break: break-all;
  background: rgba(0, 0, 0, 0.2);
  padding: 0.75rem;
  border-radius: 0.5rem;
  border: 1px solid rgba(139, 92, 246, 0.1);
}

.modern-contact-social-link {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  color: #ffffff;
  text-decoration: none;
  font-weight: 600;
  font-size: 0.9rem;
  padding: 0.75rem 1.25rem;
  background: linear-gradient(135deg, #8b5cf6 0%, #ec4899 100%);
  border: none;
  border-radius: 0.5rem;
  transition: all 0.3s ease;
  align-self: stretch;
  margin-top: auto;
  box-shadow: 0 4px 16px rgba(139, 92, 246, 0.3);
}

.modern-contact-social-link:hover {
  background: linear-gradient(135deg, #7c3aed 0%, #db2777 100%);
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(139, 92, 246, 0.4);
}

/* Enhanced Editing Experience */
.modern-contact-info-text.editable-field {
  min-height: 2.5rem;
  display: flex;
  align-items: center;
  font-size: 1rem;
}

.modern-contact-social-url.editable-field-inline {
  min-height: 2rem;
  display: flex;
  align-items: center;
  font-size: 0.875rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .modern-contact {
    padding: 3rem 0;
  }

  .modern-contact-title {
    font-size: 2.5rem;
  }

  .modern-contact-content {
    gap: 3rem;
  }

  .modern-contact-info-card,
  .modern-contact-social-card {
    padding: 1.75rem;
    min-height: 160px;
  }

  .modern-contact-social-title {
    font-size: 1.75rem;
  }

  .modern-contact-info-icon,
  .modern-contact-social-icon {
    width: 2.75rem;
    height: 2.75rem;
  }
}

@media (max-width: 480px) {
  .modern-contact-header {
    margin-bottom: 3rem;
  }

  .modern-contact-title {
    font-size: 2rem;
  }

  .modern-contact-subtitle {
    font-size: 1.125rem;
  }

  .modern-contact-info-card,
  .modern-contact-social-card {
    padding: 1.5rem;
    min-height: 140px;
  }

  .modern-contact-info-header,
  .modern-contact-social-header {
    gap: 0.75rem;
    margin-bottom: 1rem;
  }

  .modern-contact-info-icon,
  .modern-contact-social-icon {
    width: 2.5rem;
    height: 2.5rem;
  }

  .modern-contact-info-label,
  .modern-contact-social-label {
    font-size: 1.125rem;
  }

  .modern-contact-social-title {
    font-size: 1.5rem;
  }

  .modern-contact-info-link,
  .modern-contact-social-link {
    padding: 0.625rem 1rem;
    font-size: 0.875rem;
  }
}


/* ===== FOOTER STYLES ===== */
/* Modern Theme - Footer Component */

.theme-modern-footer {
  background: #1f2937;
  color: #d1d5db;
  padding: 3rem 1rem 2rem;
  text-align: center;
}

.theme-modern-footer-container {
  max-width: 1200px;
  margin: 0 auto;
}

.theme-modern-footer-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.theme-modern-footer-text {
  margin-bottom: 0;
}


/* Align “Powered by” + link on the same line */
.theme-modern-footer-attribution {
  display: flex;
  align-items: center;
  gap: 0.4rem;
  font-size: 0.875rem;
}

.theme-modern-footer-attribution a {
  text-decoration: none;
  background: linear-gradient(135deg, #60a5fa 0%, #a78bfa 50%, #f472b6 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  transition: all 0.3s ease;
  letter-spacing: -0.025em;
  display: inline-flex;
  align-items: center;
  gap: 0.4rem;
}

/* Hide 'Powered by' section in exported version */
[data-export] .theme-modern-footer-attribution {
  display: none !important;
}

.theme-modern-footer-link {
  color: #a78bfa;
  text-decoration: none;
  transition: color 0.3s ease;
}

.theme-modern-footer-logo {
  display: inline-block;
  vertical-align: middle;
  margin-right: 0.25rem;
  margin-left: 0.25rem;
}

.theme-modern-footer-link:hover {
  color: #93c5fd;
}


/* ===== BASE STYLES ===== */
/* Base styles and reset - Dark Mode */
.theme-modern-root {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
  line-height: 1.6;
  color: #e2e8f0;
  background: #0f172a;
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  scroll-behavior: smooth;
  min-height: 100vh;
}

.theme-modern-root *,
.theme-modern-root *::before,
.theme-modern-root *::after {
  box-sizing: inherit;
}

/* Container utilities */
.theme-modern-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

@media (min-width: 640px) {
  .theme-modern-container {
    padding: 0 1.5rem;
  }
}

@media (min-width: 1024px) {
  .theme-modern-container {
    padding: 0 2rem;
  }
}

/* Unified content sections background */
.theme-modern-content-sections {
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
  position: relative;
  overflow: hidden;
}

.theme-modern-content-sections::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 30%, rgba(96, 165, 250, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 70%, rgba(168, 139, 250, 0.1) 0%, transparent 50%);
  pointer-events: none;
}

/* Utility classes */
.theme-modern-btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-weight: 600;
  text-decoration: none;
  transition: all 0.3s ease;
  cursor: pointer;
  border: none;
}

.theme-modern-btn-primary {
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  color: white;
}

.theme-modern-btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(59, 130, 246, 0.4);
}

.theme-modern-btn-secondary {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.theme-modern-btn-secondary:hover {
  background: rgba(255, 255, 255, 0.2);
}

/* Animations */
@keyframes pulse {
  0%, 100% { opacity: 0.3; }
  50% { opacity: 0.1; }
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}


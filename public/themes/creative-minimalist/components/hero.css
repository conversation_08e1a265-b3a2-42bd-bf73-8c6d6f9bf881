/* Hero Section */
.theme-creative-hero {
  min-height: 100vh;
  display: flex;
  align-items: center;
  position: relative;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  overflow: hidden;
  padding-top: 5rem; /* Add padding for navbar on all screens */
}

.theme-creative-hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23e2e8f0' fill-opacity='0.4'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
  opacity: 0.5;
}

.theme-creative-hero-container {
  position: relative;
  z-index: 1;
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.theme-creative-hero-layout {
  display: grid;
  gap: 2rem;
  align-items: center;
}

@media (min-width: 768px) {
  .theme-creative-hero-layout {
    gap: 3rem;
  }
}

@media (min-width: 1024px) {
  .theme-creative-hero-layout {
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
  }
}

.theme-creative-hero-content {
  text-align: center;
  order: 2;
}

@media (min-width: 1024px) {
  .theme-creative-hero-content {
    text-align: left;
    order: 1;
  }
}

.theme-creative-hero-text {
  margin-bottom: 1.5rem;
}

@media (min-width: 768px) {
  .theme-creative-hero-text {
    margin-bottom: 2rem;
  }
}

.theme-creative-hero-title {
  font-size: 2rem;
  font-weight: 700;
  color: #111827;
  margin-bottom: 1rem;
  line-height: 1.2;
}

@media (min-width: 480px) {
  .theme-creative-hero-title {
    font-size: 2.5rem;
  }
}

@media (min-width: 640px) {
  .theme-creative-hero-title {
    font-size: 3rem;
  }
}

@media (min-width: 1024px) {
  .theme-creative-hero-title {
    font-size: 4rem;
  }
}

.theme-creative-hero-name {
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.theme-creative-hero-profession {
  margin-bottom: 1.5rem;
}

@media (min-width: 768px) {
  .theme-creative-hero-profession {
    margin-bottom: 2rem;
  }
}

.theme-creative-hero-profession-text {
  font-size: 1.125rem;
  color: #6b7280;
  font-weight: 500;
}

@media (min-width: 480px) {
  .theme-creative-hero-profession-text {
    font-size: 1.25rem;
  }
}

@media (min-width: 640px) {
  .theme-creative-hero-profession-text {
    font-size: 1.5rem;
  }
}

.theme-creative-hero-description {
  margin-bottom: 1.5rem;
}

@media (min-width: 768px) {
  .theme-creative-hero-description {
    margin-bottom: 2rem;
  }
}

.theme-creative-hero-description-text {
  font-size: 1rem;
  color: #6b7280;
  line-height: 1.6;
  margin: 0 auto;
  max-width: 600px;
}

@media (min-width: 480px) {
  .theme-creative-hero-description-text {
    font-size: 1.125rem;
  }
}

@media (min-width: 1024px) {
  .theme-creative-hero-description-text {
    margin: 0;
    max-width: none;
  }
}

.theme-creative-hero-actions {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  align-items: center;
}

@media (min-width: 480px) {
  .theme-creative-hero-actions {
    gap: 1.5rem;
  }
}

@media (min-width: 640px) {
  .theme-creative-hero-actions {
    flex-direction: row;
    justify-content: center;
    flex-wrap: wrap;
  }
}

@media (min-width: 1024px) {
  .theme-creative-hero-actions {
    justify-content: flex-start;
  }
}

.theme-creative-hero-action-icon {
  width: 1.25rem;
  height: 1.25rem;
}

/* Button Styles */
.theme-creative-btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-weight: 500;
  text-decoration: none;
  transition: all 0.3s ease;
  cursor: pointer;
  border: none;
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.theme-creative-btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.theme-creative-btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
}

.theme-creative-btn-secondary {
  background: rgba(255, 255, 255, 0.95);
  color: #4f46e5;
  border: 2px solid rgba(79, 70, 229, 0.2);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  font-weight: 600;
  position: relative;
  overflow: hidden;
}

.theme-creative-btn-secondary::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(79, 70, 229, 0.1), transparent);
  transition: left 0.5s ease;
}

.theme-creative-btn-secondary:hover {
  background: rgba(255, 255, 255, 1);
  border-color: rgba(79, 70, 229, 0.4);
  color: #3730a3;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(79, 70, 229, 0.15);
}

.theme-creative-btn-secondary:hover::before {
  left: 100%;
}

.theme-creative-btn-outline {
  background: transparent;
  color: #667eea;
  border: 2px solid #667eea;
}

.theme-creative-btn-outline:hover {
  background: #667eea;
  color: white;
  transform: translateY(-1px);
  box-shadow: 0 5px 15px rgba(102, 126, 234, 0.2);
}

.theme-creative-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
}

.theme-creative-hero-upload-btn {
  cursor: pointer;
}

.theme-creative-hero-upload-input {
  position: absolute;
  opacity: 0;
  pointer-events: none;
}

/* Editable Field Styles */
/* Hero name gets special treatment to preserve gradient */
.theme-creative-hero-name[contenteditable="true"] {
  position: relative;
  border: 2px dashed rgba(102, 126, 234, 0.3);
  border-radius: 0.375rem;
  padding: 0.5rem;
  /* No background - preserve gradient text */
  transition: all 0.3s ease;
  min-height: 1.5rem;
}

/* Other hero editable fields get standard styling */
.theme-creative-hero-profession-text[contenteditable="true"],
.theme-creative-hero-description-text[contenteditable="true"] {
  position: relative;
  border: 2px dashed rgba(102, 126, 234, 0.3);
  border-radius: 0.375rem;
  padding: 0.5rem;
  background: rgba(102, 126, 234, 0.05);
  transition: all 0.3s ease;
  min-height: 1.5rem;
}

/* Separate hover styles for hero name to preserve gradient */
.theme-creative-hero-name[contenteditable="true"]:hover {
  border-color: rgba(102, 126, 234, 0.5);
  /* Don't add background - preserve gradient text */
}

.theme-creative-hero-profession-text[contenteditable="true"]:hover,
.theme-creative-hero-description-text[contenteditable="true"]:hover {
  border-color: rgba(102, 126, 234, 0.5);
  background: rgba(102, 126, 234, 0.1);
}

/* Separate focus styles for hero name to preserve gradient */
.theme-creative-hero-name[contenteditable="true"]:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
  /* Don't add background - preserve gradient text */
}

.theme-creative-hero-profession-text[contenteditable="true"]:focus,
.theme-creative-hero-description-text[contenteditable="true"]:focus {
  outline: none;
  border-color: #667eea;
  background: rgba(102, 126, 234, 0.1);
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.theme-creative-hero-name[contenteditable="true"]:empty:before,
.theme-creative-hero-profession-text[contenteditable="true"]:empty:before,
.theme-creative-hero-description-text[contenteditable="true"]:empty:before {
  content: attr(data-placeholder);
  color: #9ca3af;
  font-style: italic;
}

/* Hero Image Styles */
.theme-creative-hero-image-container {
  position: relative;
  order: 1;
  display: flex;
  justify-content: center;
  margin-bottom: 2rem;
}

@media (min-width: 768px) {
  .theme-creative-hero-image-container {
    margin-bottom: 0;
  }
}

@media (min-width: 1024px) {
  .theme-creative-hero-image-container {
    order: 2;
    justify-content: flex-end;
  }
}

.theme-creative-hero-image-wrapper {
  position: relative;
  width: 16rem;
  height: 16rem;
  border-radius: 50%;
  overflow: hidden;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  transition: all 0.3s ease;
  cursor: pointer;
}

@media (min-width: 480px) {
  .theme-creative-hero-image-wrapper {
    width: 18rem;
    height: 18rem;
  }
}

@media (min-width: 768px) {
  .theme-creative-hero-image-wrapper {
    width: 20rem;
    height: 20rem;
  }
}

.theme-creative-hero-image-wrapper:hover {
  transform: scale(1.05);
  box-shadow: 0 35px 60px -12px rgba(0, 0, 0, 0.3);
}

.theme-creative-hero-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.theme-creative-hero-upload-overlay {
  position: absolute;
  inset: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
  color: white;
  font-weight: 500;
  font-size: 0.875rem;
  text-align: center;
}

.theme-creative-hero-upload-overlay:hover {
  opacity: 1;
}

.theme-creative-hero-upload-icon {
  width: 2rem;
  height: 2rem;
  margin-bottom: 0.5rem;
}

.theme-creative-hero-upload-loading {
  position: absolute;
  inset: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
}

.theme-creative-hero-upload-loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.75rem;
  color: white;
}

.theme-creative-hero-upload-spinner {
  width: 2rem;
  height: 2rem;
  color: white;
}

.theme-creative-hero-upload-text {
  font-weight: 500;
}

/* These styles are now handled by the unified editable field styles above */

/* Duplicate styles removed - now handled by unified editable field styles above */

/* Responsive hero spacing improvements */
@media (min-width: 480px) {
  .theme-creative-hero {
    padding-top: 5.5rem;
  }
}

@media (min-width: 768px) {
  .theme-creative-hero {
    padding-top: 6rem;
  }
}

@media (min-width: 1024px) {
  .theme-creative-hero {
    padding-top: 6.5rem;
  }
}

/* Mobile-specific hero improvements */
@media (max-width: 479px) {
  .theme-creative-hero {
    padding-top: 5rem;
  }

  .theme-creative-hero-layout {
    gap: 1.5rem;
    padding: 1rem 0;
  }

  .theme-creative-hero-title {
    font-size: 1.75rem;
  }

  .theme-creative-hero-profession-text {
    font-size: 1rem;
  }

  .theme-creative-hero-description-text {
    font-size: 0.95rem;
  }

  .theme-creative-hero-image-wrapper {
    width: 14rem;
    height: 14rem;
  }

  .theme-creative-hero-actions {
    gap: 0.5rem;
  }
}

/* Export-specific hero styles */
@media print {
  .theme-creative-hero {
    min-height: auto;
    padding: 2rem 0;
  }
}
